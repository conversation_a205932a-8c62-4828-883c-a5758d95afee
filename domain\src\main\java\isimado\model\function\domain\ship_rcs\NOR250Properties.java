package isimado.model.function.domain.ship_rcs;

import isimado.jsim.data_type.CircularPattern;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import jsim.pro.utils.UtAtmosphereData;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import static isimado.model.function.domain.AntennaPatternUtils.newCircleAntennaPatternAntenna;

@Component
public class NOR250Properties {

    @Value("${service.ship_rcs.NOR250.beamWidth}")
    private double beamWidth;
    @Value("${service.ship_rcs.NOR250.peakGain}")
    private double peakGain;

    @Value("${service.ship_rcs.NOR250.angleResolution}")
    private double angleResolution;

    @Value("${service.ship_rcs.NOR250.frequency}")
    private double frequency;

    @Value("${service.ship_rcs.NOR250.power}")
    private double power;

    @Value("${service.ship_rcs.NOR250.noisePower}")
    private double noisePower;

    @Value("${service.ship_rcs.NOR250.detectionThreshold}")
    private double detectionThreshold;

    @Value("${service.ship_rcs.NOR250.antHeight}")
    private double antHeight;

    @Value("${service.ship_rcs.NOR250.bandWidth}")
    private double bandWidth;

    @Value("${service.ship_rcs.NOR250.pulseWidth}")
    private double pulseWidth;

    @Value("${service.ship_rcs.NOR250.pulseCompressionRatio}")
    private double pulseCompressionRatio;

    @Value("${service.ship_rcs.NOR250.pulseRepetitionFrequency}")
    private double pulseRepetitionFrequency;

    public double getBeamWidth() {
        return beamWidth;
    }

    public double getPeakGain() {
        return peakGain;
    }

    public double getAngleResolution() {
        return angleResolution;
    }

    public double getFrequency() {
        return frequency;
    }

    public double getPower() {
        return power;
    }

    public double getNoisePower() {
        return noisePower;
    }

    public double getDetectionThreshold() {
        return detectionThreshold;
    }

    public double getAntHeight() {
        return antHeight;
    }

    public double getBandWidth() {
        return bandWidth;
    }

    public double getPulseWidth() {
        return pulseWidth;
    }

    public double getPulseCompressionRatio() {
        return pulseCompressionRatio;
    }

    public double getPulseRepetitionFrequency() {
        return pulseRepetitionFrequency;
    }

    public AesStandardAntennaPattern pattern(){
        CircularPattern circularPattern = new CircularPattern();
        circularPattern.setBeamwidth(beamWidth);
        circularPattern.setPeak_gain(peakGain);
        return newCircleAntennaPatternAntenna(circularPattern);
    }


}
