package isimado.model.function.domain.ship_rcs;

import imodel.jsim.function.common.*;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import jsim.utils.ut.UtEntity;
import jsim.utils.ut.UtMath;
import lombok.Getter;

@Getter
public class RadarEntity {

    private final RadarStatus radarStatus;

    private final AesStandardAntennaPattern pattern;

    private final FAAS_Antenna antenna;

    private double rangeResolution;
    private double angleResolution;

    private Xmtr xmtr;

    private Rcvr rcvr;

    private FAAS_ITU_Attenuation faasItuAttenuation;

    public RadarEntity(RadarStatus radarStatus, UtAtmosphere utAtmosphere,
                       AesEnvironment aesEnvironment,
                       NOR250Properties radar1Properties){
        this.radarStatus = radarStatus;
        LocationLLA locationLLA = radarStatus.getLocationLLA();

        this.pattern =radar1Properties.pattern();
        antenna = new StandardAntennaPatternAntenna(radarStatus.getAziScanStart(),radarStatus.getAziScanEnd(),
                -90,90, radar1Properties.getAntHeight(),0,0,pattern);
        antenna.setEntityLocation(new double[]{
                locationLLA.getLatitude(),
                locationLLA.getLongitude(),
                locationLLA.getAltitude(),
        });
        xmtr = new Xmtr(antenna, radar1Properties.getFrequency(), radar1Properties.getBandWidth(),-1,
                radar1Properties.getPower(), radar1Properties.getPulseWidth(), radar1Properties.getPulseCompressionRatio(),
                radar1Properties.getPulseRepetitionFrequency());

        rcvr = new Rcvr(antenna, radar1Properties.getFrequency(), radar1Properties.getBandWidth(),-1,
                Math.pow(10, radar1Properties.getNoisePower()*0.1)/1000,
                            Math.pow(10, radar1Properties.getDetectionThreshold()*0.1));

        double processedPulseWidth = radar1Properties.getPulseWidth() / radar1Properties.getPulseCompressionRatio();
        rangeResolution = (UtMath.cLIGHT_SPEED * processedPulseWidth) / 2.0;
        angleResolution = radar1Properties.getAngleResolution();
        faasItuAttenuation = new FAAS_ITU_Attenuation(utAtmosphere,
                radar1Properties.getFrequency(), 0, aesEnvironment);
    }

    public RadarEntity(RadarStatus radarStatus, UtAtmosphere utAtmosphere,AesEnvironment aesEnvironment
            , UPS60CProperties UPS60CProperties){
        this.radarStatus = radarStatus;
        LocationLLA locationLLA = radarStatus.getLocationLLA();

        this.pattern = UPS60CProperties.pattern();
        antenna = new StandardAntennaPatternAntenna(radarStatus.getAziScanStart(),radarStatus.getAziScanEnd(),
                -90,90, UPS60CProperties.getAntHeight(),0,0,pattern);
        antenna.setEntityLocation(new double[]{
                locationLLA.getLatitude(),
                locationLLA.getLongitude(),
                locationLLA.getAltitude(),
        });
        xmtr = new Xmtr(antenna, UPS60CProperties.getFrequency(), UPS60CProperties.getBandWidth(),-1,
                UPS60CProperties.getPower(), UPS60CProperties.getPulseWidth(), UPS60CProperties.getPulseCompressionRatio(),
                UPS60CProperties.getPulseRepetitionFrequency());

        rcvr = new Rcvr(antenna, UPS60CProperties.getFrequency(), UPS60CProperties.getBandWidth(),-1,
                Math.pow(10, UPS60CProperties.getNoisePower()*0.1)/1000,
                Math.pow(10, UPS60CProperties.getDetectionThreshold()*0.1));

        double processedPulseWidth = UPS60CProperties.getPulseWidth() / UPS60CProperties.getPulseCompressionRatio();
        rangeResolution = (UtMath.cLIGHT_SPEED * processedPulseWidth) / 2.0;
        angleResolution = UPS60CProperties.getAngleResolution();
        faasItuAttenuation = new FAAS_ITU_Attenuation(utAtmosphere,
                UPS60CProperties.getFrequency(), 0, aesEnvironment);
    }

    public String getEntityId(){
        return radarStatus.getEntityId();
    }

}
