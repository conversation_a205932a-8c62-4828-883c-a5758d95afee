package isimado.model.function.domain.ship_rcs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class RadarDetectRequest {

    @ApiModelProperty(value = "雷达站的数据", required = true)
    private List<RadarStatus> radarList;

    @ApiModelProperty(value = "舰船数据", required = true)
    private List<ShipStatus> shipList;

    @ApiModelProperty(value = "飞机数据", required = true)
    private List<AircraftStatus> aircraftList;

//    @ApiModelProperty(value = "间距 米")
//    private double distance = -1;

    @ApiModelProperty(value = "环境参数")
    private EnvironmentStatus environmentStatus;
}
