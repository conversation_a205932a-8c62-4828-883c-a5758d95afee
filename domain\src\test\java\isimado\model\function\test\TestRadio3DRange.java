package isimado.model.function.test;

import imodel.JSimPro.dataTypes.CircularPatternVO;
import imodel.jsim.function.common.*;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;

import static isimado.model.function.test.AntennaPatternUtils.newCircleAntennaPatternAntenna;

public class TestRadio3DRange {

    public static void main(String[] args) {
//        TaskService.init(10);
//
//        AesStandardAntennaPattern pattern = newCircleAntennaPatternAntenna(CircularPatternVO.newBuilder()
//                .setPeakGain(15d)
//                .setBeamwidth(5d)
//                .build());
//        FAAS_Antenna faasAntenna = new StandardAntennaPatternAntenna(-180,180,
//                -90,90,pattern);
//        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
//                225000000, 0, new AesEnvironment());
//        FAASOneWayInteraction faasRadar = new FAASOneWayInteraction(10,225000000,1e-15,
//                faasAntenna,1.5,new double[]{12.594326,118.246241,100},
//                Math.toRadians(0),
//                Math.toRadians(0),10,200000
//                ,faasItuAttenuation);
//        JammerList jammerList = new JammerList();
//        AesStandardAntennaPattern jammerPattern = newCircleAntennaPatternAntenna(CircularPatternVO.newBuilder()
//                .setPeakGain(30d)
//                .setBeamwidth(20d)
//                .build());
////        FAAS_Antenna jammerAntenna = new StandardAntennaPatternAntenna(-180,180,
////                -90,90,jammerPattern);
////        jammerList.addJammer(new FAASJammerInteraction(30,9200*1e6, jammerAntenna,new double[]{23.4,119.8,1000}, 0,0,
////                faasRadar));
//
//        double[][] rangeData = faasRadar.compute3DRange(jammerList);
//        for (double[] rangeDatum : rangeData) {
//            StringBuilder sb = new StringBuilder("[");
//            for (double v : rangeDatum) {
//                sb.append(v).append(",");
//            }
//            String result = sb.substring(0, sb.length() - 1);
//            System.out.println(result+"]");
//        }
//
////        StringBuilder sb = new StringBuilder("[");
////        double[] rangeDatum = rangeData[2];
////        int eleIndex = (0-(-90))/5;
////        int cells = 360/5+1;
////        for (int i = 0; i <= cells; i++) {
////            int index = eleIndex*cells+i;
////            sb.append(rangeDatum[index]).append(",");
////        }
////        String result = sb.substring(0, sb.length() - 1);
////        System.out.println(result+"]");
    }



}
