package isimado.model.function.domain.service.impl;

import cn.hutool.core.lang.Assert;
import imodel.jsim.function.common.*;
import isimado.framework.util.IpUtils;
import isimado.jsim.data_type.*;
import isimado.jsim.data_type.AntennaParameter;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.model.function.domain.service.RadarFunctionService;
import isimado.model.function.share.entity.JammerDTO;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import org.springframework.stereotype.Service;
import isimado.model.function.share.entity.Radar2DRangeComputeDTO;
import isimado.model.function.share.entity.Radar3DRangeComputeDTO;

import static isimado.model.function.domain.AntennaPatternUtils.newPatternAntenna;


@Service
public class RadarFunctionServiceImpl implements RadarFunctionService {


    @Override
    public double[][] compute2DRange(Radar2DRangeComputeDTO rangeComputeDTO) {
        AesStandardAntennaPattern antennaPattern = newPatternAntenna(rangeComputeDTO.getAntennaPattern());

        AntennaParameter antennaParameter = rangeComputeDTO.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna(
                antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(),
                antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),
                antennaParameter.getAntenna_height(),
                rangeComputeDTO.getActualCueAzi(),
                rangeComputeDTO.getActualCueEle(),
                antennaPattern);

        LocationLLA locationLLA = rangeComputeDTO.getLocationLLA();
        antenna.setEntityLocation(new double[]{
                locationLLA.getLatitude(),
                locationLLA.getLongitude(),
                locationLLA.getAltitude()
        });

        antenna.setEntityOrientationNED(Math.toRadians(rangeComputeDTO.getHeading()),
                Math.toRadians(rangeComputeDTO.getPitch()),0d);


        Target target = new Target();
        target.setRcs(rangeComputeDTO.getTargetRCS());
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                rangeComputeDTO.getFreqHz(), 0, new AesEnvironment());

        Xmtr xmtr = new Xmtr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange(),
                rangeComputeDTO.getXmtrPower(), 0d,0d,0d);

        Rcvr rcvr = new Rcvr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange()
                , rangeComputeDTO.getNoisePower(),rangeComputeDTO.getDetectionThread());


        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(xmtr,rcvr,faasItuAttenuation,target);

        JammerList jammerList = new JammerList();
        for (JammerDTO jammer : rangeComputeDTO.getJammers()) {
            antennaParameter = jammer.getAntennaParameter();
            antenna = new StandardAntennaPatternAntenna(
                    antennaParameter.getAzimuth_scan_limits().getFirst(),
                    antennaParameter.getAzimuth_scan_limits().getSecond(),
                    antennaParameter.getElevation_scan_limits().getFirst(),
                    antennaParameter.getElevation_scan_limits().getSecond(),
                    antennaParameter.getAntenna_height(),
                    rangeComputeDTO.getActualCueAzi(),
                    rangeComputeDTO.getActualCueEle(),
                    antennaPattern);

            locationLLA = jammer.getLocationLLA();
            antenna.setEntityLocation(new double[]{
                    locationLLA.getLatitude(),
                    locationLLA.getLongitude(),locationLLA.getAltitude()
            });

            antenna.setEntityOrientationNED(Math.toRadians(jammer.getHeading()),
                    Math.toRadians(jammer.getPitch()),0d);

            Xmtr jammerXmtr = new Xmtr(antenna,jammer.getFreqHz(),jammer.getBandwidth(),jammer.getMaxRange(),
                    jammer.getXmtrPower(), 0d,0d,0d);

            FAASJammerInteraction faasJammerInteraction = new FAASJammerInteraction(jammerXmtr,interaction,jammer.getBandWidthEffect());
            jammerList.addJammer(faasJammerInteraction);
        }
        return interaction.compute2DRange(rangeComputeDTO.getTargetHeight(), jammerList);
    }

    @Override
    public double[][] compute3DRange(Radar3DRangeComputeDTO rangeComputeDTO) {
        Assert.notNull(rangeComputeDTO.getLocationLLA(), IpUtils.getIpAddr()+":LocationLLA不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLatitude(), IpUtils.getIpAddr()+":Latitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLongitude(),IpUtils.getIpAddr()+"Longitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getAltitude(),IpUtils.getIpAddr()+"Altitude不能为空");
        AesStandardAntennaPattern antennaPattern = newPatternAntenna(rangeComputeDTO.getAntennaPattern());

        AntennaParameter antennaParameter = rangeComputeDTO.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna(
                antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(),
                antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),
                antennaParameter.getAntenna_height(),
                rangeComputeDTO.getActualCueAzi(),
                rangeComputeDTO.getActualCueEle(),
                antennaPattern);

        LocationLLA locationLLA = rangeComputeDTO.getLocationLLA();
        antenna.setEntityLocation(new double[]{locationLLA.getLatitude(),
                locationLLA.getLongitude(),locationLLA.getAltitude()});

        antenna.setEntityOrientationNED(Math.toRadians(rangeComputeDTO.getHeading()),
                Math.toRadians(rangeComputeDTO.getPitch()),0d);


        Target target = new Target();
        target.setRcs(rangeComputeDTO.getTargetRCS());
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                rangeComputeDTO.getFreqHz(), 0, new AesEnvironment());

        Xmtr xmtr = new Xmtr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange(),
                rangeComputeDTO.getXmtrPower(), 0d,0d,0d);

        Rcvr rcvr = new Rcvr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange()
                , rangeComputeDTO.getNoisePower(),rangeComputeDTO.getDetectionThread());


        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(xmtr,rcvr,faasItuAttenuation,target);

        JammerList jammerList = new JammerList();
        for (JammerDTO jammer : rangeComputeDTO.getJammers()) {
            antennaParameter = jammer.getAntennaParameter();
            antenna = new StandardAntennaPatternAntenna(
                    antennaParameter.getAzimuth_scan_limits().getFirst(),
                    antennaParameter.getAzimuth_scan_limits().getSecond(),
                    antennaParameter.getElevation_scan_limits().getFirst(),
                    antennaParameter.getElevation_scan_limits().getSecond(),
                    antennaParameter.getAntenna_height(),
                    rangeComputeDTO.getActualCueAzi(),
                    rangeComputeDTO.getActualCueEle(),
                    antennaPattern);

            locationLLA = jammer.getLocationLLA();
            antenna.setEntityLocation(new double[]{
                    locationLLA.getLatitude(),
                    locationLLA.getLongitude(),locationLLA.getAltitude()
            });

            antenna.setEntityOrientationNED(Math.toRadians(jammer.getHeading()),
                    Math.toRadians(jammer.getPitch()),0d);

            Xmtr jammerXmtr = new Xmtr(antenna,jammer.getFreqHz(),jammer.getBandwidth(),jammer.getMaxRange(),
                    jammer.getXmtrPower(), 0d,0d,0d);

            FAASJammerInteraction faasJammerInteraction = new FAASJammerInteraction(jammerXmtr,interaction,jammer.getBandWidthEffect());
            jammerList.addJammer(faasJammerInteraction);
        }
        return interaction.compute3DRange(jammerList);
    }


}
