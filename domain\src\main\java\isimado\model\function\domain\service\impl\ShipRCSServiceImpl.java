package isimado.model.function.domain.service.impl;

import imodel.jsim.function.common.*;
import isimado.framework.util.JsonUtils;
import isimado.model.function.domain.service.ShipRCSService;
import isimado.model.function.domain.ship_rcs.*;
import jsim.basic.utils.MoverUtils;
import jsim.pro.TerrainManger;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.utils.UtAtmosphere;
import jsim.pro.utils.UtAtmosphereData;
import jsim.utils.ut.*;
import morpheus.service.gis.GisService;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ShipRCSServiceImpl implements ShipRCSService {

    private AesEnvironment aesEnvironment;

    private Thread thread;

    @Resource
    private NOR250Properties nor250Properties;
    @Resource
    private UPS60CProperties ups60CProperties;

    private UtAtmosphere utAtmosphere;

    private volatile SituationResponse response =null;

    private final Object lock = new Object();

    @Resource
    private RCSService rcsService;

    private Map<String, RadarEntity> radarEntityMap = new HashMap<>();

    @PostConstruct
    public void init(){
        utAtmosphere = new UtAtmosphere(new UtAtmosphereData(), UtAtmosphere.AtmosphereType.cHOT_DAY);
        aesEnvironment = new AesEnvironment();
        aesEnvironment.mSeaState = AesEnvironment.SeaState.cROUGH;
//        thread = new Thread(this::updateLocation);
//        thread.start();
    }

    @Override
    public List<TrackList> attemptDetect(RadarDetectRequest request) {
        // 初始化探测结果列表，用于存储所有雷达站的探测结果
        List<TrackList> results = new ArrayList<>();

        // 使用同步锁保护环境参数的更新，确保线程安全
        synchronized (lock) {
            // 获取请求中的环境状态参数
            EnvironmentStatus environmentStatus = request.getEnvironmentStatus();
            if (environmentStatus != null) {
                // 更新风速参数 - 影响雷达波传播和目标探测性能
                if (environmentStatus.getWindSpeed() != null) {
                    aesEnvironment.mWindSpeed = environmentStatus.getWindSpeed();
                }
                // 更新风向参数 - 将角度转换为弧度，影响海面散射特性
                if (environmentStatus.getWindDirection() != null) {
                    aesEnvironment.mWindDirection = Math.toRadians(environmentStatus.getWindDirection());
                }
                // 更新海况等级 - 影响海面杂波和雷达探测性能
                if (environmentStatus.getSeaState() != null) {
                    aesEnvironment.mSeaState = AesEnvironment.SeaState.from(environmentStatus.getSeaState());
                }
                // 更新降雨参数 - 降雨会造成雷达波衰减，影响探测距离
                if (environmentStatus.getRainRate() != null) {
                    aesEnvironment.mRainUpperAlt = 5000; // 设置降雨层高度为5000米
                    aesEnvironment.mRainRate = environmentStatus.getRainRate(); // 设置降雨率
                }
            }
        }
        // 创建雷达实体映射表，用于存储和管理所有雷达实体
        Map<String, RadarEntity> radarEntityMap = new HashMap<>();

        // 遍历请求中的每个雷达站，为每个雷达站执行探测任务
        for (RadarStatus radarStatus : request.getRadarList()) {
            // 为当前雷达站创建探测组列表，用于存储按空间位置分组的目标
            List<DetectionGroup> detectionGroups = new ArrayList<>();

            // 根据雷达类型创建对应的雷达实体
            RadarEntity radar;
            if (radarStatus.getName().equalsIgnoreCase("NOR-250E")) {
                // 创建NOR-250E型雷达实体，使用对应的配置参数
                radar = new RadarEntity(radarStatus, utAtmosphere, aesEnvironment, nor250Properties);
            }
            else {
                // 创建UPS-60C型雷达实体（默认类型），使用对应的配置参数
                radar = new RadarEntity(radarStatus, utAtmosphere, aesEnvironment, ups60CProperties);
            }
            // 将雷达实体添加到映射表中，便于后续查找和管理
            radarEntityMap.put(radar.getEntityId(), radar);
            // 处理所有舰船目标，将其按空间位置分组
            for (ShipStatus shipStatus : request.getShipList()) {
                DetectionGroup findGroup = null; // 用于存储找到的目标组

                // 创建舰船探测实体，包含位置、航向等信息
                DetectionEntity ship = new DetectionEntity(shipStatus);

                // 遍历现有的探测组，查找该舰船是否属于某个已存在的组
                for (DetectionGroup detectionGroup : detectionGroups) {
                    // 判断舰船是否在当前探测组的角度分辨率范围内
                    if (inGroup(radar, detectionGroup, ship)) {
                        findGroup = detectionGroup; // 找到合适的组
                    }
                }

                // 如果没有找到合适的组，则创建新的探测组
                if (findGroup == null) {
                    // 获取雷达天线在世界坐标系中的位置
                    double[] locationWCS = new double[3];
                    radar.getAntenna().getEntity().GetLocationWCS(locationWCS);

                    // 获取目标舰船在世界坐标系中的位置
                    double[] tgtLocationWCS = new double[3];
                    ship.getUtEntity().GetLocationWCS(tgtLocationWCS);

                    // 计算目标相对于雷达的位置向量
                    double[] relativeLocationWCS = new double[3];
                    UtVec3d.Subtract(relativeLocationWCS, tgtLocationWCS, locationWCS);

                    // 计算目标相对于雷达的方位角和俯仰角
                    double[] doubles = radar.getAntenna().getEntity().ComputeAspect(relativeLocationWCS);
                    double azi = doubles[0]; // 方位角（弧度）
                    double ele = doubles[1]; // 俯仰角（弧度）

                    // 创建新的探测组，包含方位角、俯仰角和距离信息
                    findGroup = new DetectionGroup(azi, ele, UtVec3d.Magnitude(relativeLocationWCS));
                    detectionGroups.add(findGroup); // 将新组添加到探测组列表
                }

                // 将舰船实体添加到找到或新创建的探测组中
                findGroup.addEntity(ship);
            }

            // 处理所有飞机目标，将其按空间位置分组（处理逻辑与舰船相同）
            for (AircraftStatus shipStatus : request.getAircraftList()) {
                DetectionGroup findGroup = null; // 用于存储找到的目标组

                // 创建飞机探测实体，包含位置、航向、高度等信息
                DetectionEntity ship = new DetectionEntity(shipStatus);

                // 遍历现有的探测组，查找该飞机是否属于某个已存在的组
                for (DetectionGroup detectionGroup : detectionGroups) {
                    // 判断飞机是否在当前探测组的角度分辨率范围内
                    if (inGroup(radar, detectionGroup, ship)) {
                        findGroup = detectionGroup; // 找到合适的组
                    }
                }

                // 如果没有找到合适的组，则创建新的探测组
                if (findGroup == null) {
                    // 获取雷达天线在世界坐标系中的位置
                    double[] locationWCS = new double[3];
                    radar.getAntenna().getEntity().GetLocationWCS(locationWCS);

                    // 获取目标飞机在世界坐标系中的位置
                    double[] tgtLocationWCS = new double[3];
                    ship.getUtEntity().GetLocationWCS(tgtLocationWCS);

                    // 计算目标相对于雷达的位置向量
                    double[] relativeLocationWCS = new double[3];
                    UtVec3d.Subtract(relativeLocationWCS, tgtLocationWCS, locationWCS);

                    // 计算目标相对于雷达的方位角和俯仰角
                    double[] doubles = radar.getAntenna().getEntity().ComputeAspect(relativeLocationWCS);
                    double azi = doubles[0]; // 方位角（弧度）
                    double ele = doubles[1]; // 俯仰角（弧度）

                    // 创建新的探测组，包含方位角、俯仰角和距离信息
                    findGroup = new DetectionGroup(azi, ele, UtVec3d.Magnitude(relativeLocationWCS));
                    detectionGroups.add(findGroup); // 将新组添加到探测组列表
                }

                // 将飞机实体添加到找到或新创建的探测组中
                findGroup.addEntity(ship);
            }
            // 调用私有方法执行实际的雷达探测算法，返回探测到的航迹列表
            List<Track> tracks = attemptDetect(radar, detectionGroups);

            // 创建当前雷达站的探测结果对象
            TrackList result = new TrackList();
            result.setEntityId(radar.getEntityId()); // 设置雷达站ID
            result.setTracks(tracks); // 设置探测到的航迹列表

            // 将当前雷达站的探测结果添加到总结果列表中
            results.add(result);
        }

        // 使用同步锁更新全局响应对象和雷达实体映射表，确保线程安全

        // 创建新的态势响应对象，包含完整的探测结果和状态信息
        SituationResponse response = new SituationResponse();
        response.setTimeStamp(System.currentTimeMillis()); // 设置时间戳
        response.setTrackList(results); // 设置所有雷达站的探测结果
        response.setRadarStatusList(request.getRadarList()); // 保存雷达站状态列表
        response.setShipStatusList(request.getShipList()); // 保存舰船状态列表
        response.setAircraftStatusList(request.getAircraftList()); // 保存飞机状态列表

        // 更新全局雷达实体映射表，供其他方法使用（如计算探测距离）
        this.radarEntityMap = radarEntityMap;

        this.response = response;

        // 返回所有雷达站的探测结果列表
        return results;
    }

    public void updateLocation(){
        while (true) {
            try {
                if (response != null) {
                    synchronized (lock) {
                        long timeStamp = response.getTimeStamp();
                        long now = System.currentTimeMillis();
                        double secs = (now - timeStamp) / 1000d;
                        if (secs > 1) {
                            for (ShipStatus shipStatus : response.getShipStatusList()) {
                                double speed = shipStatus.getSpeed();
                                double[] move = MoverUtils.move(shipStatus.getLocationLLA().getLatitude(), shipStatus.getLocationLLA().getLongitude(),
                                        Math.toRadians(shipStatus.getHeading()), speed, secs);
                                shipStatus.getLocationLLA().setLatitude(move[1]);
                                shipStatus.getLocationLLA().setLongitude(move[0]);
                            }
                            for (AircraftStatus aircraftStatus : response.getAircraftStatusList()) {
                                double speed = aircraftStatus.getSpeed();
                                double[] move = MoverUtils.move(aircraftStatus.getLocationLLA().getLatitude(), aircraftStatus.getLocationLLA().getLongitude(),
                                        Math.toRadians(aircraftStatus.getHeading()), speed, secs);
                                aircraftStatus.getLocationLLA().setLatitude(move[1]);
                                aircraftStatus.getLocationLLA().setLongitude(move[0]);
                            }
                            RadarDetectRequest request = new RadarDetectRequest();
                            request.setRadarList(response.getRadarStatusList());
                            request.setShipList(response.getShipStatusList());
                            request.setAircraftList(response.getAircraftStatusList());
                            attemptDetect(request);
                        }
                    }
                }

                Thread.sleep(1000);
            } catch (RuntimeException e) {
                throw new RuntimeException(e);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public String getSituation() {
        if (response == null) {
            return null;
        }
        response.setTimeStamp(System.currentTimeMillis());
        return JsonUtils.toJson(response);
    }

    @Override
    public double[][] compute2DRange(RadarStatus radarStatus) {
        Target target = new Target();
        target.setRcs(1);
        RadarEntity radar = radarEntityMap.get(radarStatus.getEntityId());
        if (radar == null){
            return new double[0][0];
        }
        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(radar.getXmtr(),
                radar.getRcvr(),radar.getFaasItuAttenuation(),target);
        return interaction.compute2DRange(0, new JammerList());
    }

    @Override
    public double[][] compute3DRange(RadarStatus radarStatus) {
        Target target = new Target();
        target.setRcs(1);
        RadarEntity radar = radarEntityMap.get(radarStatus.getEntityId());
        if (radar == null){
            return new double[0][0];
        }
        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(radar.getXmtr(),
                radar.getRcvr(),radar.getFaasItuAttenuation(),target);
        return interaction.compute3DRange(new JammerList());
    }

    private List<Track> attemptDetect(RadarEntity radar, List<DetectionGroup> detectionGroups){
        List<Track> tracks = new ArrayList<>();
        for (DetectionGroup detectionGroup : detectionGroups){
            Target target = new Target();
            target.setRcs(detectionGroup.getRCS(rcsService,radar, radar.getXmtr().getFrequency()));

            FAAS_Antenna antenna = radar.getAntenna();
            double[] locationLLA = radar.getAntenna().getEntity().GetLocationLLA();
            antenna.setEntityLocation(locationLLA);


            FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(radar.getXmtr(),radar.getRcvr(),
                    radar.getFaasItuAttenuation(),target);

            if ((interaction.computeLimitRange(locationLLA[2], detectionGroup.getGroup().get(0).getUtEntity().mAlt)
                    > detectionGroup.getRange())){
                interaction.beginInteraction(detectionGroup.getGroup().get(0).getUtEntity().mLocationWCS);
                interaction.SetTransmitterBeamPosition();
                interaction.SetReceiverBeamPosition();
                interaction.computeRcvrJammerPower(new JammerList());
                double pulseDopplerClutter = 0d;
                if (detectionGroup.getGroup().get(0).getUtEntity().mAlt<100) {
                    pulseDopplerClutter = FAAS_EMSurface_Clutter.PulseDopplerClutter(interaction, aesEnvironment,
                            detectionGroup.getRange());
                }
                boolean canDetected = interaction.attemptDetect(detectionGroup.getGroup().get(0).getUtEntity(), pulseDopplerClutter);

                UtEntity utTarget = detectionGroup.getGroup().get(0).getUtEntity();
                canDetected&=!MaskedByTerrainFastP(radar.getAntenna().getEntity().mLat,
                        radar.getAntenna().getEntity().mLon, radar.getAntenna().getEntity().mAlt,
                        utTarget.mLat, utTarget.mLon, utTarget.mAlt, 0d, 2500);

                if (canDetected) {
                    Track track = new Track();
                    track.setEntityIds(detectionGroup.getGroup().stream().map(DetectionEntity::getEntityId).collect(Collectors.toList()));
                    tracks.add(track);
                }
            }
        }
        return tracks;
    }

    private boolean inGroup(RadarEntity radar, DetectionGroup detectionGroup, DetectionEntity newTarget){
        double[] locationWCS = new double[3];
        radar.getAntenna().getEntity().GetLocationWCS(locationWCS);
        double[] tgtLocationWCS = new double[3];
        newTarget.getUtEntity().GetLocationWCS(tgtLocationWCS);

        double[] relativeLocationWCS = new double[3];
        UtVec3d.Subtract(relativeLocationWCS,tgtLocationWCS,locationWCS);
        double[] doubles = radar.getAntenna().getEntity().ComputeAspect(relativeLocationWCS);
        double azi = doubles[0];
        if (Math.abs(Math.toDegrees(azi)-Math.toDegrees(detectionGroup.getAzimuth()))>radar.getAngleResolution()){
            return false;
        }

        for (DetectionEntity entity : detectionGroup.getGroup()) {
            entity.getUtEntity().GetLocationWCS(locationWCS);
            UtVec3d.Subtract(relativeLocationWCS,locationWCS, tgtLocationWCS);
            double distance = UtVec3d.Magnitude(relativeLocationWCS);
            if (distance<=radar.getRangeResolution()){
                return true;
            }
        }
        return false;
    }


    private boolean MaskedByTerrainFastP(double                   aLat1,
                                         double                   aLon1,
                                         double                   aAlt1,
                                         double                   aLat2,
                                         double                   aLon2,
                                         double                   aAlt2,
                                         double                   aMaxRange,
                                         int step)
    {
        boolean badData = false;

        double             terrainHeightF     = 0.f;
        double            heightAboveTerrain = 0.0;

        // Arrange the points so the first point is the lowest
        double lat1 = aLat1;
        double lon1 = aLon1;
        double alt1 = aAlt1;
        double lat2 = aLat2;
        double lon2 = aLon2;
        double alt2 = aAlt2;

        if (alt2 < alt1)
        {
            lat1 = aLat2;
            lon1 = aLon2;
            alt1 = aAlt2;
            lat2 = aLat1;
            lon2 = aLon1;
            alt2 = aAlt1;
        }

        terrainHeightF = GisService.INSTANCE.queryElevation(lon2,lat2);
        heightAboveTerrain = alt2 - terrainHeightF;
        if (heightAboveTerrain < 0.0)
        {
            if (heightAboveTerrain < -1.0)
            {
                return true;
            }
            alt2 = (terrainHeightF + TerrainManger.cSLIGHTLY_ABOVE_TERRAIN_F);
        }

        terrainHeightF = GisService.INSTANCE.queryElevation(lon1,lat1);
        // Check to see if we're over a 'hole' in the DTED data
        if (terrainHeightF < TerrainManger.cMIN_ELEV_ALLOWED_F)
        {
            badData = true;
        }

        heightAboveTerrain = alt1 - terrainHeightF;
        if (heightAboveTerrain < 0.0)
        {
            if (heightAboveTerrain < -1.0)
            {
                return true;
            }
            alt1 = terrainHeightF + TerrainManger.cSLIGHTLY_ABOVE_TERRAIN_F;
        }

        // There is a small possibility that the height adjustments made above may
        // have caused 'alt1' to become greater than 'alt2'.
        if (alt2 < alt1)
        {
            double temp = lat1;
            lat1 = lat2;
            lat2 = temp;

            temp = lon1;
            lon1 = lon2;
            lon2 = temp;

            temp = alt1;
            alt1 = alt2;
            alt2 = temp;
        }

        // If the lowest point is reasonable high above the high point then we assume
        // that terrain really has no effect or is masked by atmospherics.
        if (alt1 > 50000.0)
        {
            return false;
        }

        // Convert the source and destination positions to ECEF coordinates.
        double[] posECEF1 = new double[3];
        double[] posECEF2 = new double[3];
        UtSphericalEarth.ConvertLLAToECEF(lat1, lon1, alt1, posECEF1);
        UtSphericalEarth.ConvertLLAToECEF(lat2, lon2, alt2, posECEF2);

        // Calculate the unit vector between the source and destination positions.
        double[] unitSrcToDst = new double[3];
        UtVec3d.Set(unitSrcToDst, posECEF2[0] - posECEF1[0], posECEF2[1] - posECEF1[1], posECEF2[2] - posECEF1[2]);
        double srcToDstDist = UtVec3d.Magnitude(unitSrcToDst);

        // If we are beyond the specified maximum range, return.
        if ((aMaxRange > 0.0) && (srcToDstDist >= aMaxRange))
        {
            return true;
        }
        UtVec3d.Normalize(unitSrcToDst);

        // Set Up Variables for main loop

        double[] currPosECEF = new double[3];
        double currLat;
        double currLon;
        double currAlt;
        double stepDist;
        double currDist = 0;

        UtVec3d.Set(currPosECEF, posECEF1);

        // Start of main loop

        while (true)
        {
            if (badData)
            {
                stepDist = 30.0;
                badData  = false;
            }
            else
            {
//                double maxLatInterval = 1.0 / 3600.0; // One arcsecond == Level 2 DTED (degrees)
//                if (latInterval < maxLatInterval)
//                {
//                    // The step distance is less than the ~30m DTED level 2 limit.
//                    // Use the smaller value in the step distance computation.
//                    stepDist =
//                            Math.max(heightAboveTerrain / 3.0, latInterval * UtMath.cRAD_PER_DEG * UtSphericalEarth.cEARTH_RADIUS);
//                }
//                else // Clamp to the one arcsecond (~30m) limit
//                {
//                    stepDist = Math.max(heightAboveTerrain / 3.0, 30.0);
//                }
                stepDist=step;
            }

            currDist += stepDist;
            if (currDist >= srcToDstDist)
            {
                break;
            }

            currPosECEF[0] += stepDist * unitSrcToDst[0];
            currPosECEF[1] += stepDist * unitSrcToDst[1];
            currPosECEF[2] += stepDist * unitSrcToDst[2];

            double[] lla = UtSphericalEarth.ConvertECEFToLLA(currPosECEF);
            currLat = lla[0];
            currLon = lla[1];
            currAlt = lla[2];

            // Interpolate tempHeight from DTED file data, if we've gone off the
            // map (status != 0) load the new tile and try again.
            terrainHeightF = GisService.INSTANCE.queryElevation(currLon,currLat);

            // We need to account for the 'holes' in the DTED map data.
            // Approximately -400 meters MSL is the lowest point on Earth (Dead Sea shore in Israel)
            // In short: if the calculated height is below the Dead Sea level, we
            // know it's bad data, so don't update terrainHeightF (use the last good
            // data point known).
            if (terrainHeightF < TerrainManger.cMIN_ELEV_ALLOWED_F)
            {
                badData = true;
            }
            else
            {
                heightAboveTerrain = currAlt - terrainHeightF;
                if (heightAboveTerrain < 0.0)
                {
                    return true;
                }
            }
        }
        return false;
    }
}
