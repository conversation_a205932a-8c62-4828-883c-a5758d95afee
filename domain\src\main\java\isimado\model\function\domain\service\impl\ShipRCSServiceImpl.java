package isimado.model.function.domain.service.impl;

import imodel.jsim.function.common.*;
import isimado.framework.util.JsonUtils;
import isimado.model.function.domain.service.ShipRCSService;
import isimado.model.function.domain.ship_rcs.*;
import jsim.basic.utils.MoverUtils;
import jsim.pro.TerrainManger;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.utils.UtAtmosphere;
import jsim.pro.utils.UtAtmosphereData;
import jsim.utils.ut.*;
import morpheus.service.gis.GisService;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ShipRCSServiceImpl implements ShipRCSService {

    private AesEnvironment aesEnvironment;

    private Thread thread;

    @Resource
    private NOR250Properties nor250Properties;
    @Resource
    private UPS60CProperties ups60CProperties;

    private UtAtmosphere utAtmosphere;

    private SituationResponse response =null;

    private final Object lock = new Object();

    @Resource
    private RCSService rcsService;

    private Map<String, RadarEntity> radarEntityMap = new HashMap<>();

    @PostConstruct
    public void init(){
        utAtmosphere = new UtAtmosphere(new UtAtmosphereData(), UtAtmosphere.AtmosphereType.cHOT_DAY);
        aesEnvironment = new AesEnvironment();
        aesEnvironment.mSeaState = AesEnvironment.SeaState.cROUGH;
//        thread = new Thread(this::updateLocation);
//        thread.start();
    }

    /**
     * 雷达目标探测方法 - 执行雷达对舰船和飞机目标的探测仿真
     *
     * <p>该方法是雷达探测系统的核心功能，负责模拟雷达站对指定区域内舰船和飞机目标的探测过程。
     * 方法会根据输入的雷达参数、目标状态和环境条件，计算每个雷达站能够探测到的目标，
     * 并返回探测结果的航迹列表。</p>
     *
     * <h3>主要功能流程：</h3>
     * <ol>
     *   <li><strong>环境参数更新</strong>：根据请求中的环境状态更新全局环境参数
     *       <ul>
     *         <li>风速 (mWindSpeed)</li>
     *         <li>风向 (mWindDirection) - 自动转换为弧度</li>
     *         <li>海况等级 (mSeaState)</li>
     *         <li>降雨率 (mRainRate) 和降雨高度 (mRainUpperAlt)</li>
     *       </ul>
     *   </li>
     *   <li><strong>雷达实体初始化</strong>：为每个雷达站创建对应的雷达实体
     *       <ul>
     *         <li>根据雷达名称选择相应的配置参数 (NOR-250E 或 UPS-60C)</li>
     *         <li>关联大气模型和环境参数</li>
     *       </ul>
     *   </li>
     *   <li><strong>目标分组处理</strong>：将舰船和飞机目标按照空间位置进行分组
     *       <ul>
     *         <li>计算目标相对于雷达的方位角、俯仰角和距离</li>
     *         <li>根据雷达角度分辨率将相近目标归为同一探测组</li>
     *         <li>使用世界坐标系 (WCS) 进行精确的空间计算</li>
     *       </ul>
     *   </li>
     *   <li><strong>探测仿真执行</strong>：对每个探测组执行雷达探测算法
     *       <ul>
     *         <li>计算目标的雷达截面积 (RCS)</li>
     *         <li>考虑大气衰减、地形遮蔽等因素</li>
     *         <li>判断是否满足探测条件</li>
     *       </ul>
     *   </li>
     *   <li><strong>结果整理</strong>：将探测成功的目标组织成航迹数据并更新系统状态</li>
     * </ol>
     *
     * <h3>技术特点：</h3>
     * <ul>
     *   <li><strong>线程安全</strong>：使用 synchronized 块保护共享资源的访问</li>
     *   <li><strong>多雷达支持</strong>：可同时处理多个雷达站的探测任务</li>
     *   <li><strong>多目标类型</strong>：支持舰船和飞机两种目标类型的探测</li>
     *   <li><strong>精确建模</strong>：基于 FAAS (Fast Atmospheric Attenuation Simulation) 模型</li>
     *   <li><strong>环境适应</strong>：考虑风速、海况、降雨等环境因素对探测性能的影响</li>
     * </ul>
     *
     * <h3>坐标系统说明：</h3>
     * <ul>
     *   <li><strong>LLA坐标</strong>：经度-纬度-高度坐标系，用于地理定位</li>
     *   <li><strong>WCS坐标</strong>：世界坐标系，用于精确的空间几何计算</li>
     *   <li><strong>NED坐标</strong>：北-东-地坐标系，用于目标姿态表示</li>
     * </ul>
     *
     * @param request 雷达探测请求对象，包含以下关键信息：
     *                <ul>
     *                  <li>radarList - 参与探测的雷达站列表，每个雷达包含位置、扫描范围等参数</li>
     *                  <li>shipList - 待探测的舰船目标列表，包含位置、航向等状态信息</li>
     *                  <li>aircraftList - 待探测的飞机目标列表，包含位置、航向等状态信息</li>
     *                  <li>environmentStatus - 环境参数，包含风速、风向、海况、降雨等信息</li>
     *                  <li>distance - 目标间距参数（可选）</li>
     *                </ul>
     *
     * @return List&lt;TrackList&gt; 探测结果列表，每个 TrackList 对应一个雷达站的探测结果：
     *         <ul>
     *           <li>entityId - 雷达站的唯一标识符</li>
     *           <li>tracks - 该雷达站探测到的航迹列表，每个航迹包含：
     *               <ul>
     *                 <li>entityIds - 构成该航迹的目标实体ID列表</li>
     *                 <li>locationLLA - 航迹的地理位置信息（如果可用）</li>
     *                 <li>label - 航迹标签（如果可用）</li>
     *               </ul>
     *           </li>
     *         </ul>
     *
     * @throws RuntimeException 当雷达参数配置错误或计算过程中发生异常时抛出
     *
     * @see RadarDetectRequest 探测请求的详细参数说明
     * @see TrackList 探测结果的数据结构说明
     * @see RadarEntity 雷达实体的建模实现
     * @see DetectionGroup 目标分组的算法实现
     * @see FAASTwoWayInteraction FAAS双向交互模型的探测算法
     *
     * @since 1.0
     * <AUTHOR>
     */
    @Override
    public List<TrackList> attemptDetect(RadarDetectRequest request) {
        List<TrackList> results = new ArrayList<>();

        synchronized (lock) {
            EnvironmentStatus environmentStatus = request.getEnvironmentStatus();
            if (environmentStatus != null) {
                if (environmentStatus.getWindSpeed() != null) {
                    aesEnvironment.mWindSpeed = environmentStatus.getWindSpeed();
                }
                if (environmentStatus.getWindDirection() != null) {
                    aesEnvironment.mWindDirection = Math.toRadians(environmentStatus.getWindDirection());
                }
                if (environmentStatus.getSeaState() != null) {
                    aesEnvironment.mSeaState = AesEnvironment.SeaState.from(environmentStatus.getSeaState());
                }
                if (environmentStatus.getRainRate() != null) {
                    aesEnvironment.mRainUpperAlt = 5000;
                    aesEnvironment.mRainRate = environmentStatus.getRainRate();
                }
            }
        }
        Map<String, RadarEntity> radarEntityMap = new HashMap<>();
        for (RadarStatus radarStatus : request.getRadarList()) {
            List<DetectionGroup> detectionGroups = new ArrayList<>();
            RadarEntity radar;
            if (radarStatus.getName().equalsIgnoreCase("NOR-250E")) {
                radar = new RadarEntity(radarStatus, utAtmosphere, aesEnvironment, nor250Properties);
            }
            else {
                radar = new RadarEntity(radarStatus, utAtmosphere, aesEnvironment, ups60CProperties);
            }
            radarEntityMap.put(radar.getEntityId(), radar);
            for (ShipStatus shipStatus : request.getShipList()) {
                DetectionGroup findGroup = null;
                DetectionEntity ship = new DetectionEntity(shipStatus);
                for (DetectionGroup detectionGroup : detectionGroups) {
                    if (inGroup(radar, detectionGroup, ship)) {
                        findGroup = detectionGroup;
                    }
                }
                if (findGroup == null) {
                    double[] locationWCS = new double[3];
                    radar.getAntenna().getEntity().GetLocationWCS(locationWCS);
                    double[] tgtLocationWCS = new double[3];
                    ship.getUtEntity().GetLocationWCS(tgtLocationWCS);
                    double[] relativeLocationWCS = new double[3];
                    UtVec3d.Subtract(relativeLocationWCS, tgtLocationWCS, locationWCS);
                    double[] doubles = radar.getAntenna().getEntity().ComputeAspect(relativeLocationWCS);
                    double azi = doubles[0];
                    double ele = doubles[1];
                    findGroup = new DetectionGroup(azi,ele, UtVec3d.Magnitude(relativeLocationWCS));
                    detectionGroups.add(findGroup);
                }
                findGroup.addEntity(ship);
            }

            for (AircraftStatus shipStatus : request.getAircraftList()) {
                DetectionGroup findGroup = null;
                DetectionEntity ship = new DetectionEntity(shipStatus);
                for (DetectionGroup detectionGroup : detectionGroups) {
                    if (inGroup(radar, detectionGroup, ship)) {
                        findGroup = detectionGroup;
                    }
                }
                if (findGroup == null) {
                    double[] locationWCS = new double[3];
                    radar.getAntenna().getEntity().GetLocationWCS(locationWCS);
                    double[] tgtLocationWCS = new double[3];
                    ship.getUtEntity().GetLocationWCS(tgtLocationWCS);
                    double[] relativeLocationWCS = new double[3];
                    UtVec3d.Subtract(relativeLocationWCS, tgtLocationWCS, locationWCS);
                    double[] doubles = radar.getAntenna().getEntity().ComputeAspect(relativeLocationWCS);
                    double azi = doubles[0];
                    double ele = doubles[1];
                    findGroup = new DetectionGroup(azi,ele, UtVec3d.Magnitude(relativeLocationWCS));
                    detectionGroups.add(findGroup);
                }
                findGroup.addEntity(ship);
            }
            List<Track> tracks = attemptDetect(radar, detectionGroups);
            TrackList result = new TrackList();
            result.setEntityId(radar.getEntityId());
            result.setTracks(tracks);
            results.add(result);
        }
        synchronized (lock) {
            response = new SituationResponse();
            response.setTimeStamp(System.currentTimeMillis());
            response.setTrackList(results);
            response.setRadarStatusList(request.getRadarList());
            response.setShipStatusList(request.getShipList());
            response.setAircraftStatusList(request.getAircraftList());
            this.radarEntityMap = radarEntityMap;
        }
        return results;
    }

    public void updateLocation(){
        while (true) {
            try {
                if (response != null) {
                    synchronized (lock) {
                        long timeStamp = response.getTimeStamp();
                        long now = System.currentTimeMillis();
                        double secs = (now - timeStamp) / 1000d;
                        if (secs > 1) {
                            for (ShipStatus shipStatus : response.getShipStatusList()) {
                                double speed = shipStatus.getSpeed();
                                double[] move = MoverUtils.move(shipStatus.getLocationLLA().getLatitude(), shipStatus.getLocationLLA().getLongitude(),
                                        Math.toRadians(shipStatus.getHeading()), speed, secs);
                                shipStatus.getLocationLLA().setLatitude(move[1]);
                                shipStatus.getLocationLLA().setLongitude(move[0]);
                            }
                            for (AircraftStatus aircraftStatus : response.getAircraftStatusList()) {
                                double speed = aircraftStatus.getSpeed();
                                double[] move = MoverUtils.move(aircraftStatus.getLocationLLA().getLatitude(), aircraftStatus.getLocationLLA().getLongitude(),
                                        Math.toRadians(aircraftStatus.getHeading()), speed, secs);
                                aircraftStatus.getLocationLLA().setLatitude(move[1]);
                                aircraftStatus.getLocationLLA().setLongitude(move[0]);
                            }
                            RadarDetectRequest request = new RadarDetectRequest();
                            request.setRadarList(response.getRadarStatusList());
                            request.setShipList(response.getShipStatusList());
                            request.setAircraftList(response.getAircraftStatusList());
                            attemptDetect(request);
                        }
                    }
                }

                Thread.sleep(1000);
            } catch (RuntimeException e) {
                throw new RuntimeException(e);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public String getSituation() {
        synchronized (lock) {
            return JsonUtils.toJson(response);
        }
    }

    @Override
    public double[][] compute2DRange(RadarStatus radarStatus) {
        Target target = new Target();
        target.setRcs(1);
        RadarEntity radar = radarEntityMap.get(radarStatus.getEntityId());
        if (radar == null){
            return new double[0][0];
        }
        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(radar.getXmtr(),
                radar.getRcvr(),radar.getFaasItuAttenuation(),target);
        return interaction.compute2DRange(0, new JammerList());
    }

    @Override
    public double[][] compute3DRange(RadarStatus radarStatus) {
        Target target = new Target();
        target.setRcs(1);
        RadarEntity radar = radarEntityMap.get(radarStatus.getEntityId());
        if (radar == null){
            return new double[0][0];
        }
        FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(radar.getXmtr(),
                radar.getRcvr(),radar.getFaasItuAttenuation(),target);
        return interaction.compute3DRange(new JammerList());
    }

    private List<Track> attemptDetect(RadarEntity radar, List<DetectionGroup> detectionGroups){
        List<Track> tracks = new ArrayList<>();
        for (DetectionGroup detectionGroup : detectionGroups){
            Target target = new Target();
            target.setRcs(detectionGroup.getRCS(rcsService,radar, radar.getXmtr().getFrequency()));

            FAAS_Antenna antenna = radar.getAntenna();
            double[] locationLLA = radar.getAntenna().getEntity().GetLocationLLA();
            antenna.setEntityLocation(locationLLA);


            FAASTwoWayInteraction interaction = new FAASTwoWayInteraction(radar.getXmtr(),radar.getRcvr(),
                    radar.getFaasItuAttenuation(),target);

            if ((interaction.computeLimitRange(locationLLA[2], detectionGroup.getGroup().get(0).getUtEntity().mAlt)
                    > detectionGroup.getRange())){
                interaction.beginInteraction(detectionGroup.getGroup().get(0).getUtEntity().mLocationWCS);
                interaction.SetTransmitterBeamPosition();
                interaction.SetReceiverBeamPosition();
                interaction.computeRcvrJammerPower(new JammerList());
                double pulseDopplerClutter = 0d;
                if (detectionGroup.getGroup().get(0).getUtEntity().mAlt<100) {
                    pulseDopplerClutter = FAAS_EMSurface_Clutter.PulseDopplerClutter(interaction, aesEnvironment,
                            detectionGroup.getRange());
                }
                boolean canDetected = interaction.attemptDetect(detectionGroup.getGroup().get(0).getUtEntity(), pulseDopplerClutter);

                UtEntity utTarget = detectionGroup.getGroup().get(0).getUtEntity();
                canDetected&=!MaskedByTerrainFastP(radar.getAntenna().getEntity().mLat,
                        radar.getAntenna().getEntity().mLon, radar.getAntenna().getEntity().mAlt,
                        utTarget.mLat, utTarget.mLon, utTarget.mAlt, 0d, 2500);

                if (canDetected) {
                    Track track = new Track();
                    track.setEntityIds(detectionGroup.getGroup().stream().map(DetectionEntity::getEntityId).collect(Collectors.toList()));
                    tracks.add(track);
                }
            }
        }
        return tracks;
    }

    private boolean inGroup(RadarEntity radar, DetectionGroup detectionGroup, DetectionEntity newTarget){
        double[] locationWCS = new double[3];
        radar.getAntenna().getEntity().GetLocationWCS(locationWCS);
        double[] tgtLocationWCS = new double[3];
        newTarget.getUtEntity().GetLocationWCS(tgtLocationWCS);

        double[] relativeLocationWCS = new double[3];
        UtVec3d.Subtract(relativeLocationWCS,tgtLocationWCS,locationWCS);
        double[] doubles = radar.getAntenna().getEntity().ComputeAspect(relativeLocationWCS);
        double azi = doubles[0];
        if (Math.abs(Math.toDegrees(azi)-Math.toDegrees(detectionGroup.getAzimuth()))>radar.getAngleResolution()){
            return false;
        }

        for (DetectionEntity entity : detectionGroup.getGroup()) {
            entity.getUtEntity().GetLocationWCS(locationWCS);
            UtVec3d.Subtract(relativeLocationWCS,locationWCS, tgtLocationWCS);
            double distance = UtVec3d.Magnitude(relativeLocationWCS);
            if (distance<=radar.getRangeResolution()){
                return true;
            }
        }
        return false;
    }


    private boolean MaskedByTerrainFastP(double                   aLat1,
                                         double                   aLon1,
                                         double                   aAlt1,
                                         double                   aLat2,
                                         double                   aLon2,
                                         double                   aAlt2,
                                         double                   aMaxRange,
                                         int step)
    {
        boolean badData = false;

        double             terrainHeightF     = 0.f;
        double            heightAboveTerrain = 0.0;

        // Arrange the points so the first point is the lowest
        double lat1 = aLat1;
        double lon1 = aLon1;
        double alt1 = aAlt1;
        double lat2 = aLat2;
        double lon2 = aLon2;
        double alt2 = aAlt2;

        if (alt2 < alt1)
        {
            lat1 = aLat2;
            lon1 = aLon2;
            alt1 = aAlt2;
            lat2 = aLat1;
            lon2 = aLon1;
            alt2 = aAlt1;
        }

        terrainHeightF = GisService.INSTANCE.queryElevation(lon2,lat2);
        heightAboveTerrain = alt2 - terrainHeightF;
        if (heightAboveTerrain < 0.0)
        {
            if (heightAboveTerrain < -1.0)
            {
                return true;
            }
            alt2 = (terrainHeightF + TerrainManger.cSLIGHTLY_ABOVE_TERRAIN_F);
        }

        terrainHeightF = GisService.INSTANCE.queryElevation(lon1,lat1);
        // Check to see if we're over a 'hole' in the DTED data
        if (terrainHeightF < TerrainManger.cMIN_ELEV_ALLOWED_F)
        {
            badData = true;
        }

        heightAboveTerrain = alt1 - terrainHeightF;
        if (heightAboveTerrain < 0.0)
        {
            if (heightAboveTerrain < -1.0)
            {
                return true;
            }
            alt1 = terrainHeightF + TerrainManger.cSLIGHTLY_ABOVE_TERRAIN_F;
        }

        // There is a small possibility that the height adjustments made above may
        // have caused 'alt1' to become greater than 'alt2'.
        if (alt2 < alt1)
        {
            double temp = lat1;
            lat1 = lat2;
            lat2 = temp;

            temp = lon1;
            lon1 = lon2;
            lon2 = temp;

            temp = alt1;
            alt1 = alt2;
            alt2 = temp;
        }

        // If the lowest point is reasonable high above the high point then we assume
        // that terrain really has no effect or is masked by atmospherics.
        if (alt1 > 50000.0)
        {
            return false;
        }

        // Convert the source and destination positions to ECEF coordinates.
        double[] posECEF1 = new double[3];
        double[] posECEF2 = new double[3];
        UtSphericalEarth.ConvertLLAToECEF(lat1, lon1, alt1, posECEF1);
        UtSphericalEarth.ConvertLLAToECEF(lat2, lon2, alt2, posECEF2);

        // Calculate the unit vector between the source and destination positions.
        double[] unitSrcToDst = new double[3];
        UtVec3d.Set(unitSrcToDst, posECEF2[0] - posECEF1[0], posECEF2[1] - posECEF1[1], posECEF2[2] - posECEF1[2]);
        double srcToDstDist = UtVec3d.Magnitude(unitSrcToDst);

        // If we are beyond the specified maximum range, return.
        if ((aMaxRange > 0.0) && (srcToDstDist >= aMaxRange))
        {
            return true;
        }
        UtVec3d.Normalize(unitSrcToDst);

        // Set Up Variables for main loop

        double[] currPosECEF = new double[3];
        double currLat;
        double currLon;
        double currAlt;
        double stepDist;
        double currDist = 0;

        UtVec3d.Set(currPosECEF, posECEF1);

        // Start of main loop

        while (true)
        {
            if (badData)
            {
                stepDist = 30.0;
                badData  = false;
            }
            else
            {
//                double maxLatInterval = 1.0 / 3600.0; // One arcsecond == Level 2 DTED (degrees)
//                if (latInterval < maxLatInterval)
//                {
//                    // The step distance is less than the ~30m DTED level 2 limit.
//                    // Use the smaller value in the step distance computation.
//                    stepDist =
//                            Math.max(heightAboveTerrain / 3.0, latInterval * UtMath.cRAD_PER_DEG * UtSphericalEarth.cEARTH_RADIUS);
//                }
//                else // Clamp to the one arcsecond (~30m) limit
//                {
//                    stepDist = Math.max(heightAboveTerrain / 3.0, 30.0);
//                }
                stepDist=step;
            }

            currDist += stepDist;
            if (currDist >= srcToDstDist)
            {
                break;
            }

            currPosECEF[0] += stepDist * unitSrcToDst[0];
            currPosECEF[1] += stepDist * unitSrcToDst[1];
            currPosECEF[2] += stepDist * unitSrcToDst[2];

            double[] lla = UtSphericalEarth.ConvertECEFToLLA(currPosECEF);
            currLat = lla[0];
            currLon = lla[1];
            currAlt = lla[2];

            // Interpolate tempHeight from DTED file data, if we've gone off the
            // map (status != 0) load the new tile and try again.
            terrainHeightF = GisService.INSTANCE.queryElevation(currLon,currLat);

            // We need to account for the 'holes' in the DTED map data.
            // Approximately -400 meters MSL is the lowest point on Earth (Dead Sea shore in Israel)
            // In short: if the calculated height is below the Dead Sea level, we
            // know it's bad data, so don't update terrainHeightF (use the last good
            // data point known).
            if (terrainHeightF < TerrainManger.cMIN_ELEV_ALLOWED_F)
            {
                badData = true;
            }
            else
            {
                heightAboveTerrain = currAlt - terrainHeightF;
                if (heightAboveTerrain < 0.0)
                {
                    return true;
                }
            }
        }
        return false;
    }
}
