package isimado.model.function.domain.service.impl;

import imodel.jsim.function.common.FAASInteraction;
import jsim.basic.utils.Tuple2;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesAntennaPattern;
import jsim.pro.sensor.antenna.AesEMAntenna;
import jsim.pro.sensor.clutter.AesMIT_ClutterStrength;
import jsim.utils.ut.UtEllipsoidalEarth;
import jsim.utils.ut.UtMat3d;
import jsim.utils.ut.UtMath;
import jsim.utils.ut.UtVec3d;
import static jsim.pro.sensor.clutter.AesMIT_ClutterStrength.GetSeaClutterStrength;

public class FAAS_EMSurface_Clutter {

    public static double[] sWaveHeight = new double[]{
            0d,0.152,0.457,0.762,1.22,1.82,3.049,0d,0d,0d,
            0d,0d,   0d,   0d,   0d,  0d,  0d,   0d,0d,0d
    };


    public static double ContinuousWaveClutter(FAASInteraction aInteraction, AesEnvironment aEnvironment,
                                         double xmtrBeamAzi,
                                         double rcvrBeamAzi) {
//        IXmtr xmtrPtr = aInteraction.getTransmitter();
//        AesEM_Rcvr rcvrPtr = aInteraction.GetReceiver();

        double clutterW = 1e-30;
        //计算杂波横向距离（同时计算其他辅助变量）
        double[] viewVecNE = {0,0,0};
        double[] geometryResult = ComputeGeometry(aInteraction, aEnvironment, viewVecNE);
        double ha = geometryResult[0];
        double re = geometryResult[1];
        double clutterHorizonRange = geometryResult[2];
        double depressionAngle = geometryResult[3];

        //分成100个步长计算杂波
        double clutterStepSize = clutterHorizonRange / 100d;

        AesAntennaPattern<?> patternPtr = aInteraction.GetXmtrAntennaPattern();

        double azBeamwidth = 0d;
        if (patternPtr != null){
            azBeamwidth = patternPtr.GetAzimuthBeamwidth(aInteraction.getXmtr().getFrequency(), 0d, 0d);
        }
        if (azBeamwidth < 1e-3* UtMath.cRAD_PER_DEG){
            return clutterW;
        }

        double azBeamwidth2 = 0.71 * UtMath.cRAD_PER_DEG;
        double clutterCellArea = azBeamwidth2 * clutterStepSize;

        double rn = ha + 1d;
        double loopVar = clutterHorizonRange * 2d;
        double minimumRange = 200d;

        rn -= clutterStepSize;
        while (rn <= loopVar){
            rn += clutterStepSize;
            if (rn < minimumRange){
                continue;
            }

            double clutterGrazingAngle = Math.asin((ha * (1 + (ha / (2 * re)))/rn) - rn / (2 * re));
            if (clutterGrazingAngle < 0){
                clutterGrazingAngle = 0d;
            }



            double[] vecNED = new double[]{viewVecNE[0], viewVecNE[1], Math.sin(depressionAngle)};
            double[] vecWCS = {0,0,0};

            aInteraction.ConvertNED_ToWCS(vecNED, vecWCS);
            double lat = 0d;
            double lon = 0d;
            double alt = 0d;
            double[] lla = UtEllipsoidalEarth.ConvertECEFToLLA(vecWCS,lat, lon, alt);
            lat = lla[0];
            lon = lla[1];
            alt = lla[2];

            double z = GetSeaClutterStrength(clutterGrazingAngle,
                    aEnvironment.GetSeaState(), aInteraction.getXmtr().getFrequency(), 0);
            double sig0 = Math.pow(10.0, (z / 10.0));

            // Compute clutter power returned from range rn - pc1(watts)
            // based on Nathanson pg 66, eqns 2-39 & 2-40
            //
            // Assume clutter cell limited by range cell size (clutterStepSize)
            double cellRCS = sig0 * clutterCellArea * rn / Math.cos(clutterGrazingAngle);
            double pc1     = ComputeRF_TwoWayPower(aInteraction, viewVecNE, depressionAngle, rn, cellRCS,xmtrBeamAzi,
                    rcvrBeamAzi);

            // auto out = ut::log::info() << "Continuous Wave Clutter:";
            // out.AddNote() << "PC1: " << UtMath::LinearToDB(pc1) << " dB";
            // out.AddNote() << "Sig0: " << sig0; 
            // out.AddNote() << "R: " << rn;
            // out.AddNote() << "GA: " << clutterGrazingAngle * UtMath::cDEG_PER_RAD << " deg";
            // out.AddNote() << "DA: " << depressionAngle * UtMath::cDEG_PER_RAD << " deg"
            // out.AddNote() << "CCS: " << cellRCS;

            // Sum clutter from all range cells - pclut(watts)
            clutterW += pc1;
        }
        return clutterW;
    }

    public static double PulseDopplerClutter(FAASInteraction aInteraction, AesEnvironment aEnvironment, double range) {
        double clutterW = 1.0E-30;

        // Compute range resolution for clutter cell size - resolution(m)
        double pulseWidth = aInteraction.getXmtr().getPulseWidth();
        if (pulseWidth == 0.0)
        {
            // No pulse width... assume matched filter.
            double bandwidth = aInteraction.getXmtr().getBandWidth();
            if (bandwidth <= 0.0)
            {
                return clutterW;
            }
            pulseWidth = 1.0 / bandwidth;
        }
        pulseWidth /= aInteraction.getXmtr().getPulseCompressionRatio(); // The processed pulse width
        double resolution = (UtMath.cLIGHT_SPEED * pulseWidth) / 2.0;

        // Assume clutter cell limited by pulse length
        AesAntennaPattern<?> patternPtr  = aInteraction.GetXmtrAntennaPattern();
        double azBeamwidth = patternPtr.GetAzimuthBeamwidth(aInteraction.getXmtr().getFrequency(), 0.0, 0.0);
        if (azBeamwidth < (0.001 * UtMath.cRAD_PER_DEG))
        {
            return clutterW;
        }
        double azBeamWidth2    = 0.71 * azBeamwidth;
        double clutterCellArea = resolution * azBeamWidth2;

        double slantRange = range;

        // Compute the unambiguous range
        double prf              = aInteraction.getXmtr().getPulseRepetitionFrequency();
        double unambiguousRange = UtMath.cLIGHT_SPEED / (2.0 * prf);

        // Compute radar blind range
        double blindRange = 0.1 * unambiguousRange;

        // Compute clutter horizon range (and other auxiliary variables)
        double[] viewVecNE = new double[2];
        double[] geometryResult = ComputeGeometry(aInteraction, aEnvironment, viewVecNE);
        double ha = geometryResult[0];
        double re = geometryResult[1];
        double clutterHorizonRange = geometryResult[2];
        double depressionAngle = geometryResult[3];

        // Determine number of ambiguous ranges at which clutter responses will be generated - inumb1
        double maximumRange = Math.max(2.0 * clutterHorizonRange, slantRange);
        double anumb        = maximumRange / unambiguousRange;
        int    inumb        = Double.valueOf(anumb + 0.5).intValue();
        int    inumb1       = (2 * inumb) + 1;

        double minimumRange = ha + 1.0;
        // Loop for summation of clutter contributions from ambiguous ranges
        for (int ii = 1; ii <= inumb1; ++ii)
        {
            int i = ii - (inumb + 1);

            // Compute slant range from which clutter is received - rn (meters)
            double rn = slantRange - (unambiguousRange * i);
            if (rn < minimumRange)
            {
                continue;
            }
            // Check if high prf return falls inside radar blind range
            if ((prf >= 6.0E+3) && (blindRange > rn))
            {
                continue;
            }
            if ((rn > clutterHorizonRange))
            {
                continue;
            }

            // Compute radar grazing angle at clutter spot and radar beam depression angle to same spot.
            // Don't allow radar beam to look through the earth's limb

            double clutterGrazingAngle = Math.asin((ha * (1.0 + (ha / (2.0 * re))) / rn) - (rn / (2.0 * re)));
            if (clutterGrazingAngle < 0.0)
            {
                clutterGrazingAngle = 0.0;
            }

            // Compute clutter backscatter coefficient - z(db) & sig0(pwr)
            double z;
            z = AesMIT_ClutterStrength.GetSeaClutterStrength(clutterGrazingAngle,
                    aEnvironment.GetSeaState(),
                    aInteraction.getXmtr().getFrequency(),
                    0);

            double sig0 = Math.pow(10.0, (z / 10.0));

            // Compute clutter power returned from range rn - pc1(watts)
            // based on Nathanson pg 66, eqns 2-39 & 2-40
            // Assume clutter cell limited by pulse length
            double cellRCS = sig0 * clutterCellArea * rn / Math.cos(clutterGrazingAngle);
            double pc1     = ComputeRF_TwoWayPower(aInteraction, viewVecNE, depressionAngle, rn, cellRCS, 0,0);

            // auto out = ut::log::info() << "Pulse Doppler Clutter:";
            // out.AddNote() << "PC1: " << UtMath::LinearToDB(pc1) << " dB";
            // out.AddNote() << "Sig0: " << sig0;
            // out.AddNote() << "R: " << rn;
            // out.AddNote() << "GA: " << clutterGrazingAngle * UtMath::cDEG_PER_RAD << " deg";
            // out.AddNote() << "DA: " << depressionAngle * UtMath::cDEG_PER_RAD << " deg"
            // out.AddNote() << "CCS: " << cellRCS;

            // Sum clutter from all range cells -aPClut(watts)
            clutterW += pc1;
        }
        return clutterW;
    }

    private static double[] ComputeGeometry(FAASInteraction aInteraction,
                                     AesEnvironment aEnvironment,
                                     double[] aViewVecNE){
////        IXmtr xmtrPtr = aInteraction.getTransmitter();
        double[] lla = aInteraction.getXmtr().getAntenna().getEntity().GetLocationLLA();
//
        double alt = aInteraction.getXmtr().getAntenna().getHeight();

        double elevation = FAASInteraction.terrainInterface.queryElevation(lla[1], lla[0]);
        double antennaHeight = Math.max(alt - elevation,1d);

        double earthRadius = 8476090 * (3d/4d) * 1;

        double adjustedAntennaHeight;
        double adjustedEarthRadius;
//        if (PlatformUtils.isOnSurface(xmtrPtr.getPlatform())){
//            int seaState = aEnvironment.GetSeaState().getCode();
//            seaState = Math.min(Math.max(seaState, 0),
//                    AesEnvironment.SeaState.cNUM_SEA_STATES.getCode()-1);
//            adjustedAntennaHeight = antennaHeight - sWaveHeight[seaState];
//            adjustedEarthRadius = earthRadius + sWaveHeight[seaState];
//        } else {
            int landFormation = aEnvironment.GetLandFormation().getCode();
            landFormation = Math.min(Math.max(landFormation, 0),
                    AesEnvironment.LandFormation.cNUM_LAND_FORMS.getCode()-1);
            adjustedAntennaHeight = antennaHeight + (sWaveHeight[9 + landFormation]/2d);
            adjustedEarthRadius = earthRadius + (sWaveHeight[9 + landFormation]/2d);
//        }
        double clutterHorizonRange = 4123 * Math.sqrt(Math.max(adjustedAntennaHeight, 0d));
        double[] viewVecBCS = new double[]{1, 0, 0};
        double[] viewVecWCS = new double[]{0,0,0};
        double[] viewVecNED = new double[]{0,0,0};
        UtMat3d.InverseTransform(viewVecWCS, aInteraction.mRcvrBeam.mWCS_ToBeamTransform, viewVecBCS);
        aInteraction.ConvertWCS_VectorToNED(viewVecWCS, viewVecNED);

        double depressionAngle = Math.asin(viewVecNED[2]);

        viewVecNED[2] = 0d;
        UtVec3d.Normalize(viewVecNED);
        aViewVecNE[0] = viewVecNED[0];
        aViewVecNE[1] = viewVecNED[1];

        return new double[]{adjustedAntennaHeight, adjustedEarthRadius, clutterHorizonRange, depressionAngle};
    }

    // =================================================================================================
//! Computes the power received from a clutter cell.
//! @param aInteraction     The interaction object.
//! @param aViewVecNE       The line of sight viewing vector (see ComputeGeometry for details).
//! @param aDepressionAngle The depression angle to the clutter cell (radians).
//! @param aSlantRange      The slant range to the clutter cell (meters).
//! @param aCrossSection    The effective radar cross section the clutter cell (meters^2).
//! @returns the received clutter power (Watts).
// private
    private static double ComputeRF_TwoWayPower(FAASInteraction aInteraction,
                                         double[]       aViewVecNE,
                                         double             aDepressionAngle,
                                         double             aSlantRange,
                                         double             aCrossSection,
                                         double xmtrBeamAzi,
                                         double rcvrBeamAzi)
    {
//        IXmtr xmtrPtr = aInteraction.GetTransmitter();
//        AesEM_Rcvr rcvrPtr = aInteraction.GetReceiver();

        // Compute the elevation angle of the clutter cell with respect to the beam center.
        //
        // aViewVecNE is a unit vector that points in the horizontal direction from the receiver
        // along the line-of-sight towards the target (See ComputeGeometry). The vertical component
        // will now be computed to now point at the clutter cell.

        double[] clutterVecNED = {aViewVecNE[0], aViewVecNE[1], Math.sin(aDepressionAngle)};
        double[] clutterVecWCS = new double[3];
        aInteraction.ConvertNED_VectorToWCS(clutterVecNED, clutterVecWCS);

        double beamToClutterAz =0d;
        double beamToClutterEl= 0d;
        double[] beamAspect = AesEMAntenna.ComputeBeamAspect(aInteraction.mRcvrBeam.mWCS_ToBeamTransform,
                clutterVecWCS,
                beamToClutterAz,
                beamToClutterEl);

        beamToClutterAz = beamAspect[0];
        beamToClutterEl = beamAspect[1];

        // Get radiated power (watts)
        double[] xmtdPowerAndGain = ComputeRadiatedPower(aInteraction,xmtrBeamAzi,
                beamToClutterEl,
                aInteraction.getXmtr().getFrequency());
        double xmtdPower = xmtdPowerAndGain[0];
        // Compute attenuation factor (assume monostatic)
        //
        // SALRAM did not perform atmospheric attenuation on the clutter, so neither will we...
        double attenuation = 1.0;

        // Propagate signal to clutter cell
        double p_density_at_tgt = xmtdPower * attenuation / (UtMath.cFOUR_PI * aSlantRange * aSlantRange);

        // Compute reflected signal from clutter cell
        double p_reflected = p_density_at_tgt * aCrossSection;

        // Propagate reflected signal to receiver
        double p_density_at_rcvr = p_reflected * attenuation / (UtMath.cFOUR_PI * aSlantRange * aSlantRange);

        // Receive the signal
        return ComputeReceivedPower(aInteraction,rcvrBeamAzi,
                beamToClutterEl,
                p_density_at_rcvr,
                aInteraction.getXmtr().getFrequency()).f1;
    }

    static double[] ComputeRadiatedPower(FAASInteraction interaction,
                                  double aTargetAz, double aTargetEl, double aFrequency) {
        // Compute the antenna gain.
        double aAntennaGain = interaction.getXmtr().getAntenna().getXmtrGain(aFrequency,aTargetAz,aTargetEl);
        double powerDensity = interaction.getXmtr().getPower() * aAntennaGain ;
        return new double[]{powerDensity, aAntennaGain};
    }

    static Tuple2<Double, Double> ComputeReceivedPower(FAASInteraction interaction,double aSourceAz,
                                                double aSourceEl,
                                                double aReceivedPowerDensity,
                                                double aFrequency) {
        // Compute the antenna gain.
        double aAntennaGain = interaction.getXmtr().getAntenna().getXmtrGain(aFrequency, aSourceAz, aSourceEl);
        double wavelength = UtMath.cLIGHT_SPEED / aFrequency;
        double temp1 = wavelength * wavelength / UtMath.cFOUR_PI;
        double receivedPower = aReceivedPowerDensity * temp1 * aAntennaGain ;
        return new Tuple2<>(aAntennaGain, receivedPower);
    }
}
