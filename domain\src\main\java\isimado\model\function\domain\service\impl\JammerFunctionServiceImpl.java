package isimado.model.function.domain.service.impl;

import cn.hutool.core.lang.Assert;
import imodel.jsim.function.common.*;
import isimado.framework.util.IpUtils;
import isimado.jsim.data_type.AntennaParameter;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.jsim.data_type.LocationLLA;
import isimado.model.function.domain.service.JammerFunctionService;
import isimado.model.function.share.entity.Jammer2DRangeComputeDTO;
import isimado.model.function.share.entity.Jammer3DRangeComputeDTO;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import org.springframework.stereotype.Service;

import static isimado.model.function.domain.AntennaPatternUtils.newPatternAntenna;

@Service
public class JammerFunctionServiceImpl implements JammerFunctionService {
    @Override
    public double[][] compute2DRange(Jammer2DRangeComputeDTO rangeComputeDTO) {
        Assert.notNull(rangeComputeDTO.getLocationLLA(), IpUtils.getIpAddr()+":LocationLLA不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLatitude(), IpUtils.getIpAddr()+":Latitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLongitude(),IpUtils.getIpAddr()+"Longitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getAltitude(),IpUtils.getIpAddr()+"Altitude不能为空");
        AntennaPatternVariant pattern = rangeComputeDTO.getAntennaPattern();
        AesStandardAntennaPattern antennaPattern = newPatternAntenna(pattern);

        AntennaParameter antennaParameter = rangeComputeDTO.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna( antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(),
                antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),
                antennaParameter.getAntenna_height(),
                rangeComputeDTO.getActualCueAzi(),
                rangeComputeDTO.getActualCueEle(),
                antennaPattern);

        LocationLLA locationLLA = rangeComputeDTO.getLocationLLA();
        antenna.setEntityLocation(new double[]{
                locationLLA.getLatitude(),
                locationLLA.getLongitude(),
                locationLLA.getAltitude()
        });

        antenna.setEntityOrientationNED(Math.toRadians(rangeComputeDTO.getHeading()),
                Math.toRadians(rangeComputeDTO.getPitch()),0d);
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                rangeComputeDTO.getFreqHz(), 0, new AesEnvironment());
        double noisePower = Math.pow(10, rangeComputeDTO.getMinPower()*0.1)/1000;


        Xmtr xmtr = new Xmtr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange(),
                rangeComputeDTO.getXmtrPower(), 0d,0d,0d);

        Rcvr rcvr = new Rcvr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange()
                , noisePower,2d);

        FAASOneWayInteraction interaction = new FAASOneWayInteraction(xmtr,rcvr,faasItuAttenuation);
        return interaction.computeRangeForPower2D(rangeComputeDTO.getTargetHeight(),
                noisePower);
    }

    @Override
    public double[][] compute3DRange(Jammer3DRangeComputeDTO rangeComputeDTO) {
        Assert.notNull(rangeComputeDTO.getLocationLLA(), IpUtils.getIpAddr()+":LocationLLA不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLatitude(), IpUtils.getIpAddr()+":Latitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLongitude(),IpUtils.getIpAddr()+"Longitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getAltitude(),IpUtils.getIpAddr()+"Altitude不能为空");
        AntennaPatternVariant pattern = rangeComputeDTO.getAntennaPattern();
        AesStandardAntennaPattern antennaPattern = newPatternAntenna(pattern);

        AntennaParameter antennaParameter = rangeComputeDTO.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna( antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(),
                antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),
                antennaParameter.getAntenna_height(),
                rangeComputeDTO.getActualCueAzi(),
                rangeComputeDTO.getActualCueEle(),
                antennaPattern);

        LocationLLA locationLLA = rangeComputeDTO.getLocationLLA();
        antenna.setEntityLocation(new double[]{
                locationLLA.getLatitude(),
                locationLLA.getLongitude(),
                locationLLA.getAltitude()
        });

        antenna.setEntityOrientationNED(Math.toRadians(rangeComputeDTO.getHeading()),
                Math.toRadians(rangeComputeDTO.getPitch()),0d);
        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                rangeComputeDTO.getFreqHz(), 0, new AesEnvironment());
        double noisePower = Math.pow(10, rangeComputeDTO.getMinPower()*0.1)/1000;


        Xmtr xmtr = new Xmtr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange(),
                rangeComputeDTO.getXmtrPower(), 0d,0d,0d);

        Rcvr rcvr = new Rcvr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange()
                , noisePower,2d);

        FAASOneWayInteraction interaction = new FAASOneWayInteraction(xmtr,rcvr,faasItuAttenuation);
        return interaction.computeRangeForPower3D(noisePower);
    }
}
