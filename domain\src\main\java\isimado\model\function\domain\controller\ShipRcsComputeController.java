package isimado.model.function.domain.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import isimado.framework.base.Resp;
import isimado.framework.util.JsonUtils;
import isimado.model.function.domain.ship_rcs.*;
import isimado.model.function.domain.service.ShipRCSService;
import jsim.basic.utils.MoverUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "小艇接口")
@ApiSupport(order = 1, author = "clyu")
@RestController
@Slf4j
@RequestMapping("ship_rcs")
public class ShipRcsComputeController {

    @Resource
    private ShipRCSService shipRcsService;

    @ApiOperation(value = "探测结果")
    @ApiOperationSupport(order = 100)
    @PostMapping("attemptDetected")
    public Resp<RadarDetectResponse> attemptDetected(@RequestBody RadarDetectRequest dto) {
        RadarDetectResponse response = new RadarDetectResponse();
        response.setTracks(shipRcsService.attemptDetect(dto));
        return Resp.success(response);
    }

    @ApiOperation(value = "态势展示")
    @ApiOperationSupport(order = 100)
    @PostMapping("getSituation")
    public Resp<SituationResponse> getSituation() {
        SituationResponse response = JsonUtils.parse(shipRcsService.getSituation(), SituationResponse.class);
//        response.setTimeStamp(System.currentTimeMillis());
        return Resp.success(response);
    }

    @ApiOperation(value = "雷达包络2D")
    @ApiOperationSupport(order = 100)
    @PostMapping("2D")
    public Resp<double[][]> range2D(@RequestBody RadarStatus dto) {
        return Resp.success(shipRcsService.compute2DRange(dto));
    }


    @ApiOperation(value = "探测结果")
    @ApiOperationSupport(order = 100)
    @PostMapping("simpleAttemptDetected")
    public Resp<RadarDetectResponse> simpleAttemptDetected(@RequestBody SimpleRadarDetectRequest dto) {

        RadarDetectRequest request = new RadarDetectRequest();
        request.setRadarList(new ArrayList<>(){{
            add(dto.getRadar());
        }});

        double[] move = MoverUtils.move(dto.getRadar().getLocationLLA().getLatitude(),
                dto.getRadar().getLocationLLA().getLongitude(),
                0, dto.getDistance(), 1);
        double startLon = move[0];
        double startLat = move[1];
        List<ShipStatus> shipEntityList = new ArrayList<>();
        double heading = dto.getHeading();
        if (dto.isHorizontal()){
            heading = heading+90;
        }
        for (int i = 0; i<dto.getShipCount();i++){

            move = MoverUtils.move(startLat,
                    startLon,
                    Math.toRadians(heading), dto.getShipDistance(), i);

            ShipStatus shipStatus = new ShipStatus();
            shipStatus.setEntityId(String.valueOf(i));
            shipStatus.setHeading(Math.toRadians(dto.getHeading()));
            shipStatus.setSpeed(30d);
            LocationLLA locationLLA = new LocationLLA();
            locationLLA.setLongitude(move[0]);
            locationLLA.setLatitude(move[1]);
            locationLLA.setAltitude(0d);
            shipStatus.setLocationLLA(locationLLA);
            shipEntityList.add(shipStatus);
        }
        request.setShipList(shipEntityList);
        RadarDetectResponse response = new RadarDetectResponse();
        response.setTracks(shipRcsService.attemptDetect(request));
        return Resp.success(response);
    }

    @ApiOperation(value = "雷达包络3D")
    @ApiOperationSupport(order = 100)
    @PostMapping("3D")
    public Resp<double[][]> range3D(@RequestBody RadarStatus dto) {
        return Resp.success(shipRcsService.compute3DRange(dto));
    }
}
