package isimado.model.function.share.entity;

import io.swagger.annotations.ApiModelProperty;
import isimado.jsim.data_type.AntennaParameter;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.jsim.data_type.LocationLLA;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 计算雷达2D包络
 */
@Data
public class Radar3DRangeComputeDTO {

    @ApiModelProperty(value = "雷达所在平台的经纬高", required = true)
    private LocationLLA locationLLA;

    @ApiModelProperty(value = "雷达所在平台的朝向，度数", required = true)
    private double heading;

    @ApiModelProperty(value = "雷达所在平台的俯仰角，度数", required = true)
    private double pitch;

    @ApiModelProperty("雷达天线参数")
    private AntennaParameter antennaParameter;


    @ApiModelProperty(value = "发射机功率(W)", required = true)
    private double xmtrPower;

    @ApiModelProperty(value = "发射机天线方向图参数", required = true)
    private AntennaPatternVariant antennaPattern;

    @ApiModelProperty(value = "发射机工作频率(HZ)", required = true)
    private double freqHz;

    @ApiModelProperty(value = "带宽(HZ)", required = true)
    private double bandwidth;

    @ApiModelProperty("目标等效反射面积(m²),默认值10")
    private double targetRCS = 10;

    @ApiModelProperty(value = "雷达热噪声",required = true)
    private double noisePower;

    @ApiModelProperty(value = "SNR阈值",required = true)
    private double detectionThread;

    @ApiModelProperty(value = "最大值",required = true)
    private double maxRange;

    @ApiModelProperty(value = "干扰机状态",required = false)
    private List<JammerDTO> jammers = new ArrayList<>();

    @ApiModelProperty(value = "干扰机水平指向")
    private double actualCueAzi = 0;

    @ApiModelProperty(value = "干扰机垂直指向")
    private double actualCueEle = 0;
}


