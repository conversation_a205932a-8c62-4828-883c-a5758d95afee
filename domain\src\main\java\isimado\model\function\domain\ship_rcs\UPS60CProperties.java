package isimado.model.function.domain.ship_rcs;

import isimado.jsim.data_type.CircularPattern;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static isimado.model.function.domain.AntennaPatternUtils.newCircleAntennaPatternAntenna;

@Component
public class UPS60CProperties {

    @Value("${service.ship_rcs.UPS60C.beamWidth}")
    private double beamWidth;
    @Value("${service.ship_rcs.UPS60C.peakGain}")
    private double peakGain;

    @Value("${service.ship_rcs.UPS60C.angleResolution}")
    private double angleResolution;

    @Value("${service.ship_rcs.UPS60C.frequency}")
    private double frequency;

    @Value("${service.ship_rcs.UPS60C.power}")
    private double power;

    @Value("${service.ship_rcs.UPS60C.noisePower}")
    private double noisePower;

    @Value("${service.ship_rcs.UPS60C.detectionThreshold}")
    private double detectionThreshold;

    @Value("${service.ship_rcs.UPS60C.antHeight}")
    private double antHeight;

    @Value("${service.ship_rcs.UPS60C.bandWidth}")
    private double bandWidth;

    @Value("${service.ship_rcs.UPS60C.pulseWidth}")
    private double pulseWidth;

    @Value("${service.ship_rcs.UPS60C.pulseCompressionRatio}")
    private double pulseCompressionRatio;

    @Value("${service.ship_rcs.UPS60C.pulseRepetitionFrequency}")
    private double pulseRepetitionFrequency;

    public double getBeamWidth() {
        return beamWidth;
    }

    public double getPeakGain() {
        return peakGain;
    }

    public double getAngleResolution() {
        return angleResolution;
    }

    public double getFrequency() {
        return frequency;
    }

    public double getPower() {
        return power;
    }

    public double getNoisePower() {
        return noisePower;
    }

    public double getDetectionThreshold() {
        return detectionThreshold;
    }

    public double getAntHeight() {
        return antHeight;
    }

    public double getBandWidth() {
        return bandWidth;
    }

    public double getPulseWidth() {
        return pulseWidth;
    }

    public double getPulseCompressionRatio() {
        return pulseCompressionRatio;
    }

    public double getPulseRepetitionFrequency() {
        return pulseRepetitionFrequency;
    }

    public AesStandardAntennaPattern pattern(){
        CircularPattern circularPattern = new CircularPattern();
        circularPattern.setBeamwidth(beamWidth);
        circularPattern.setPeak_gain(peakGain);
        return newCircleAntennaPatternAntenna(circularPattern);
    }


}
