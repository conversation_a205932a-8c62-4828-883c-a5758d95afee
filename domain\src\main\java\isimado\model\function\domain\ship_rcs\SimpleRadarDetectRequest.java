package isimado.model.function.domain.ship_rcs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SimpleRadarDetectRequest {

    @ApiModelProperty(value = "雷达站的数据", required = true)
    private RadarStatus radar;

    @ApiModelProperty(value = "舰船个数", required = true)
    private int shipCount;

    @ApiModelProperty(value = "航向", required = true)
    private double heading = 0;

    @ApiModelProperty(value = "舰船距离雷达的距离", required = true)
    private double shipDistance = 0;

    @ApiModelProperty(value = "舰船距离雷达的距离", required = true)
    private double distance ;

    @ApiModelProperty(value = "是否横列", required = true)
    private boolean horizontal;
}
