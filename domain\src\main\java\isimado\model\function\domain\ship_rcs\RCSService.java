package isimado.model.function.domain.ship_rcs;

import cn.hutool.json.JSONUtil;
import imodel.JSimPro.dataTypes.*;
import jsim.pro.sensor.signature.AesStandardRadarSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Service
public class RCSService {

    @Value("${service.ship_rcs.rcs.ship.top}")
    String shipTop = "";
    @Value("${service.ship_rcs.rcs.ship.left}")
    String shipLeft = "";
    @Value("${service.ship_rcs.rcs.ship.front}")
    String shipFront = "";
    @Value("${service.ship_rcs.rcs.ship.backend}")
    String shipBackend = "";

    AesStandardRadarSignature shipStandardRadarSignature;

    AesRadarSignatureShareDataVO shipSignaturePtr;


    @Value("${service.ship_rcs.rcs.aircraft.top}")
    String aircraftTop = "";
    @Value("${service.ship_rcs.rcs.aircraft.left}")
    String aircraftLeft = "";
    @Value("${service.ship_rcs.rcs.aircraft.front}")
    String aircraftFront = "";
    @Value("${service.ship_rcs.rcs.aircraft.backend}")
    String aircraftBackend = "";

    AesStandardRadarSignature aircraftStandardRadarSignature;

    AesRadarSignatureShareDataVO aircraftSignaturePtr;
    @PostConstruct
    public void init() {
        List<String> inline_table = new ArrayList<>();

        inline_table.add("state default");
        inline_table.add("inline_table dBsm 6 4");
        inline_table.add("-90 -30  30  90");
        inline_table.add("-180 top  backend backend  top");
        inline_table.add("-160 top  left left  top");
        inline_table.add("-30 top  front  front  top");
        inline_table.add("30 top  front  front top");
        inline_table.add("160 top left left top");
        inline_table.add("180 top backend  backend top");
        inline_table.add("end_inline_table");
        shipSignaturePtr = AesRadarSignatureShareDataVO.newBuilder()
                .setMSates(new ArrayList<>() {{
                    add(AesRadarSignatureShareDataStateVO.newBuilder()
                            .setMPolarization(new ArrayList<>())
                            .build());
                }})
                .setMMonoStaticSigDefined(true)
                .build();
        AesRadarSignatureVO signatureVO = AesRadarSignatureVO.newBuilder()
                .setMSignaturePtr(shipSignaturePtr)
                .build();
        String jsonStr = JSONUtil.toJsonStr(inline_table);
        jsonStr = jsonStr.replace("top", shipTop);
        jsonStr = jsonStr.replace("left", shipLeft);
        jsonStr = jsonStr.replace("backend", shipBackend);
        jsonStr = jsonStr.replace("front", shipFront);

        RadarSignatureVO aInput = RadarSignatureVO.newBuilder()
                .setInlineTable(jsonStr)
                .setState(new ArrayList<>(){{
                    add(AesRadarSignatureStateVO.newBuilder()
                            .setStateName("default")
                            .setPolarization("")
                            .setFrequencyLimit("")
                            .build());
                }})
                .build();
        shipStandardRadarSignature = new AesStandardRadarSignature();
        shipStandardRadarSignature.ProcessInput(signatureVO, aInput);


        aircraftSignaturePtr = AesRadarSignatureShareDataVO.newBuilder()
                .setMSates(new ArrayList<>() {{
                    add(AesRadarSignatureShareDataStateVO.newBuilder()
                            .setMPolarization(new ArrayList<>())
                            .build());
                }})
                .setMMonoStaticSigDefined(true)
                .build();
        signatureVO = AesRadarSignatureVO.newBuilder()
                .setMSignaturePtr(aircraftSignaturePtr)
                .build();
        jsonStr = JSONUtil.toJsonStr(inline_table);
        jsonStr = jsonStr.replace("top", aircraftTop);
        jsonStr = jsonStr.replace("left", aircraftLeft);
        jsonStr = jsonStr.replace("backend", aircraftBackend);
        jsonStr = jsonStr.replace("front", aircraftFront);

        aInput = RadarSignatureVO.newBuilder()
                .setInlineTable(jsonStr)
                .setState(new ArrayList<>(){{
                    add(AesRadarSignatureStateVO.newBuilder()
                            .setStateName("default")
                            .setPolarization("")
                            .setFrequencyLimit("")
                            .build());
                }})
                .build();
        aircraftStandardRadarSignature = new AesStandardRadarSignature();
        aircraftStandardRadarSignature.ProcessInput(signatureVO, aInput);
    }

    public double getSignature(String aStateId,
                               int aPolarization,
                               double aFrequency,
                               double aTgtToXmtrAz,
                               double aTgtToXmtrEl,
                               double aTgtToRcvrAz,
                               double aTgtToRcvrEl,
                               boolean isShip
    ) {

        if (isShip) {
            return shipStandardRadarSignature.getSignature(aStateId,
                    aPolarization,
                    aFrequency,
                    aTgtToXmtrAz,
                    aTgtToXmtrEl,
                    aTgtToRcvrAz,
                    aTgtToRcvrEl,
                    shipSignaturePtr);
        }
        return aircraftStandardRadarSignature.getSignature(aStateId,
                aPolarization,
                aFrequency,
                aTgtToXmtrAz,
                aTgtToXmtrEl,
                aTgtToRcvrAz,
                aTgtToRcvrEl,
                aircraftSignaturePtr);
    }
}
