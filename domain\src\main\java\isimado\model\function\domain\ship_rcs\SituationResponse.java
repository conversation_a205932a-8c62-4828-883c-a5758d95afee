package isimado.model.function.domain.ship_rcs;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SituationResponse {

    private long TimeStamp;

    private List<TrackList> trackList = new ArrayList<>();

    private List<RadarStatus> radarStatusList = new ArrayList<>();

    private List<ShipStatus> shipStatusList = new ArrayList<>();

    private List<AircraftStatus> aircraftStatusList = new ArrayList<>();
}
