package isimado.model.function.domain.ship_rcs;

import jsim.utils.ut.UtEntity;

public class DetectionEntity {
    private String entityId;

    private boolean shipFlag;

    private UtEntity utEntity;

    public DetectionEntity(ShipStatus shipStatus) {
        this.entityId = shipStatus.getEntityId();
        this.shipFlag = true;
        utEntity = new UtEntity();
        LocationLLA locationLLA = shipStatus.getLocationLLA();
        utEntity.SetLocationLLA(locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude());
        utEntity.GetLocationWCS(new double[3]);
        utEntity.SetOrientationNED(Math.toRadians(shipStatus.getHeading()),
                0,0);
    }

    public DetectionEntity(AircraftStatus aircraftStatus) {
        this.entityId = aircraftStatus.getEntityId();
        this.shipFlag = false;
        utEntity = new UtEntity();
        LocationLLA locationLLA = aircraftStatus.getLocationLLA();
        utEntity.SetLocationLLA(locationLLA.getLatitude(), locationLLA.getLongitude(), locationLLA.getAltitude());
        utEntity.GetLocationWCS(new double[3]);
        utEntity.SetOrientationNED(Math.toRadians(aircraftStatus.getHeading()),
                0,0);
    }

    public UtEntity getUtEntity() {
        return utEntity;
    }

    public String getEntityId(){
        return entityId;
    }

    public boolean isShipFlag() {
        return shipFlag;
    }
}
