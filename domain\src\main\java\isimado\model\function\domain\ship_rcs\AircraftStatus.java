package isimado.model.function.domain.ship_rcs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AircraftStatus {

    @ApiModelProperty(value = "唯一ID", required = true)
    private String entityId;

    @ApiModelProperty(value = "平台的经纬高", required = true)
    private LocationLLA locationLLA;

    @ApiModelProperty(value = "速度", required = true)
    private double speed;

    @ApiModelProperty(value = "航向角,东北天坐标系，0度为正北，顺时针为正，逆时针为负", required = true)
    private double heading;

    @ApiModelProperty(value = "属方ID",required = true)
    private String forceId;

    @ApiModelProperty(value = "类型名称",required = true)
    private String name;
}
