spring:
  application:
    name: isimado-function
  cloud:
    nacos:
      server-addr: ***********:8848
      group: MNXL_GROUP
      discovery:
        group: ${spring.cloud.nacos.group}
        enabled: false
      config:
        file-extension: yaml
        group: ${spring.cloud.nacos.group}
  main:
    allow-circular-references: true

service:
  ship_rcs:
    NOR250:
      #角分辨率
      angleResolution: 1
      #带宽 雷达信号的频率范围宽度
      bandWidth: 1e6

      #脉冲宽度  距离分辨率
      pulseWidth: 1e-6
      pulseCompressionRatio: 5
      pulseRepetitionFrequency: 1.5e3

      ##雷达工作频率
      frequency: 1.5e9
      power: 42000

      #噪声功率
      noisePower: -97
      #探测阈值: 是指雷达系统能够检测到目标信号的最小信号强度或最小信噪比
      detectionThreshold: 3
      #峰值增益
      peakGain: 35

      #波束宽度  主波瓣的半功率波瓣宽度
      beamWidth: 1
      antHeight: 10
    UPS60C:
      angleResolution: 1
      bandWidth: 1e6
      pulseWidth: 1e-6
      pulseCompressionRatio: 5
      pulseRepetitionFrequency: 1.5e3
      frequency: 1.5e9
      power: 86000
      noisePower: -97
      detectionThreshold: 3
      peakGain: 35
      beamWidth: 1
      antHeight: 10
    rcs:
      aircraft:
        top: 10
        left: 8
        front: 0.5
        backend: 2
      ship:
        top: 5
        left: 5
        front: 0.25
        backend: 0.5
knife4j:
  enable: on
