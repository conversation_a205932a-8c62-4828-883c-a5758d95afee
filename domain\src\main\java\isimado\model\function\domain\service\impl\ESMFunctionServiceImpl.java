package isimado.model.function.domain.service.impl;

import cn.hutool.core.lang.Assert;
import imodel.jsim.function.common.*;
import isimado.framework.util.IpUtils;
import isimado.jsim.data_type.AntennaParameter;
import isimado.jsim.data_type.AntennaPatternVariant;
import isimado.jsim.data_type.LocationLLA;
import isimado.jsim.data_type.UniformPattern;
import isimado.model.function.domain.service.ESMFunctionService;
import isimado.model.function.share.entity.*;
import jsim.pro.enviorment.AesEnvironment;
import jsim.pro.sensor.antenna.AesStandardAntennaPattern;
import jsim.pro.utils.UtAtmosphere;
import org.springframework.stereotype.Service;

import static isimado.model.function.domain.AntennaPatternUtils.newPatternAntenna;
import static isimado.model.function.domain.AntennaPatternUtils.newUniformPattern;


@Service
public class ESMFunctionServiceImpl implements ESMFunctionService {


    @Override
    public double[][] compute2DRange(ESM2DRangeComputeDTO rangeComputeDTO) {
        UniformPattern pattern = new UniformPattern();
        pattern.setAzimuth_beamwidth(360d);
        pattern.setElevation_beamwidth(180d);
        pattern.setPeak_gain(rangeComputeDTO.getGain());
        AesStandardAntennaPattern antennaPattern = newUniformPattern(pattern);

        AntennaParameter antennaParameter = rangeComputeDTO.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna(
                antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(),
                antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),
                antennaParameter.getAntenna_height(),
                rangeComputeDTO.getActualCueAzi(),
                rangeComputeDTO.getActualCueEle(),
                antennaPattern);

        LocationLLA locationLLA = rangeComputeDTO.getLocationLLA();
        antenna.setEntityLocation(new double[]{
                locationLLA.getLatitude(),
                locationLLA.getLongitude(),
                locationLLA.getAltitude()
        });

        antenna.setEntityOrientationNED(Math.toRadians(rangeComputeDTO.getHeading()),
                Math.toRadians(rangeComputeDTO.getPitch()),0d);

        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                rangeComputeDTO.getFreqHz(), 0, new AesEnvironment());

        Xmtr xmtr = new Xmtr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange(),
                rangeComputeDTO.getXmtrPower(), 0d,0d,0d);

        Rcvr rcvr = new Rcvr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange()
                , rangeComputeDTO.getNoisePower(),rangeComputeDTO.getDetectionThread());

        FAASOneWayInteraction interaction = new FAASOneWayInteraction(xmtr,rcvr,faasItuAttenuation);

        return interaction.compute2DRange(rangeComputeDTO.getTargetHeight(),new JammerList());
    }

    @Override
    public double[][] compute3DRange(ESM3DRangeComputeDTO rangeComputeDTO) {
        Assert.notNull(rangeComputeDTO.getLocationLLA(), IpUtils.getIpAddr()+":LocationLLA不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLatitude(), IpUtils.getIpAddr()+":Latitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getLongitude(),IpUtils.getIpAddr()+"Longitude不能为空");
        Assert.notNull(rangeComputeDTO.getLocationLLA().getAltitude(),IpUtils.getIpAddr()+"Altitude不能为空");

        UniformPattern pattern = new UniformPattern();
        pattern.setAzimuth_beamwidth(360d);
        pattern.setElevation_beamwidth(180d);
        pattern.setPeak_gain(rangeComputeDTO.getGain());
        AesStandardAntennaPattern antennaPattern = newUniformPattern(pattern);

        AntennaParameter antennaParameter = rangeComputeDTO.getAntennaParameter();
        FAAS_Antenna antenna = new StandardAntennaPatternAntenna(
                antennaParameter.getAzimuth_scan_limits().getFirst(),
                antennaParameter.getAzimuth_scan_limits().getSecond(),
                antennaParameter.getElevation_scan_limits().getFirst(),
                antennaParameter.getElevation_scan_limits().getSecond(),
                antennaParameter.getAntenna_height(),
                rangeComputeDTO.getActualCueAzi(),
                rangeComputeDTO.getActualCueEle(),
                antennaPattern);

        LocationLLA locationLLA = rangeComputeDTO.getLocationLLA();
        antenna.setEntityLocation(new double[]{
                locationLLA.getLatitude(),
                locationLLA.getLongitude(),
                locationLLA.getAltitude()
        });

        antenna.setEntityOrientationNED(Math.toRadians(rangeComputeDTO.getHeading()),
                Math.toRadians(rangeComputeDTO.getPitch()),0d);

        FAAS_ITU_Attenuation faasItuAttenuation = new FAAS_ITU_Attenuation(UtAtmosphere.CreateNewAtmosphereTable(),
                rangeComputeDTO.getFreqHz(), 0, new AesEnvironment());

        Xmtr xmtr = new Xmtr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange(),
                rangeComputeDTO.getXmtrPower(), 0d,0d,0d);

        Rcvr rcvr = new Rcvr(antenna,rangeComputeDTO.getFreqHz(),rangeComputeDTO.getBandwidth(),rangeComputeDTO.getMaxRange()
                , rangeComputeDTO.getNoisePower(),rangeComputeDTO.getDetectionThread());

        FAASOneWayInteraction interaction = new FAASOneWayInteraction(xmtr,rcvr,faasItuAttenuation);

        return interaction.compute3DRange(new JammerList());
    }

}
